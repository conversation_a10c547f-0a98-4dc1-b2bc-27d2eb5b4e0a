<template>
  <div class="java-chapter6">
    <!-- 页面头部 -->
    <div class="chapter-header">
      <div class="container">
        <div class="header-content">
          <h1 class="chapter-title">第六章：JDK并发库 (JDK Concurrency Libraries)</h1>
          <p class="chapter-subtitle">从原子操作到异步编程：现代并发工具全景</p>
          <div class="chapter-badge">
            <span class="badge-text">JUC Concurrency</span>
          </div>
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: progress + '%' }"></div>
            <span class="progress-text">{{ progress }}% 完成</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容 -->
    <div class="container">
      <div class="content-wrapper">
        <div class="content-layout">
          <!-- 侧边栏导航 -->
          <aside class="sidebar">
            <div class="outline">
              <h3>📚 章节大纲</h3>
              <div class="outline-grid">
                <div
                  v-for="(topic, index) in courseTopics"
                  :key="index"
                  @click="scrollToTopic(index)"
                  :class="['outline-item', { active: currentTopic === index }]"
                >
                  <div class="outline-number">{{ index + 1 }}</div>
                  <div class="outline-content">
                    <h4>{{ topic.title }}</h4>
                    <p>{{ topic.description }}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- 工具栏 -->
            <div class="toolbar">
              <button @click="toggleNotes" class="tool-button">📝 笔记</button>
              <button @click="showQuiz" class="tool-button">🧠 测验</button>
            </div>
          </aside>

          <!-- 主内容区 -->
          <main class="main-content">
            <!-- Topic 1: 现代并发应用构建基石 -->
            <section id="topic-0" class="topic-section" ref="topic0">
              <ExpandableSection
                title="现代并发应用构建基石 (Building blocks for modern concurrent applications)"
                :concept-data="modernConcurrencyData"
                @interaction="handleInteraction"
              >
                <div class="modern-concurrency-showcase">
                  <h3>🏗️ 现代并发应用构建基石</h3>

                  <div class="juc-evolution">
                    <h4>📈 JUC包的诞生背景</h4>
                    <div class="evolution-timeline">
                      <div class="timeline-item past">
                        <div class="timeline-header">
                          <span class="timeline-icon">⚰️</span>
                          <h5>Java 1.4及之前</h5>
                        </div>
                        <div class="timeline-content">
                          <p>只有原始的并发工具</p>
                          <div class="evolution-details">
                            <h6>可用工具</h6>
                            <ul>
                              <li>synchronized关键字</li>
                              <li>wait/notify机制</li>
                              <li>Thread类基本操作</li>
                              <li>volatile变量</li>
                            </ul>
                            <h6>主要限制</h6>
                            <ul>
                              <li>功能单一，表达力不足</li>
                              <li>性能开销较大</li>
                              <li>复杂协作难以实现</li>
                              <li>容易出错，调试困难</li>
                            </ul>
                          </div>
                        </div>
                      </div>

                      <div class="timeline-arrow">→</div>

                      <div class="timeline-item present">
                        <div class="timeline-header">
                          <span class="timeline-icon">🚀</span>
                          <h5>Java 5+ (JUC时代)</h5>
                        </div>
                        <div class="timeline-content">
                          <p>专业并发工具库</p>
                          <div class="evolution-details">
                            <h6>核心组件</h6>
                            <ul>
                              <li>原子类 (Atomic*)</li>
                              <li>显式锁 (Lock/Condition)</li>
                              <li>并发容器 (ConcurrentHashMap等)</li>
                              <li>线程池 (Executor框架)</li>
                              <li>同步器 (CountDownLatch等)</li>
                            </ul>
                            <h6>设计理念</h6>
                            <ul>
                              <li>分离关注点与显式化</li>
                              <li>基于CAS的无锁算法</li>
                              <li>AQS框架的统一抽象</li>
                              <li>函数式异步编程</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="juc-principles">
                    <h4>💡 JUC设计原理</h4>
                    <div class="principles-grid">
                      <div class="principle-card performance">
                        <div class="principle-header">
                          <span class="principle-icon">⚡</span>
                          <h5>高性能</h5>
                        </div>
                        <div class="principle-content">
                          <p>通过无锁算法和精细化锁定策略实现最优性能</p>
                          <div class="principle-details">
                            <h6>核心技术</h6>
                            <ul>
                              <li>CAS (Compare-And-Swap) 原子操作</li>
                              <li>分段锁 (Lock Striping)</li>
                              <li>写时复制 (Copy-On-Write)</li>
                              <li>无锁数据结构</li>
                            </ul>
                          </div>
                        </div>
                      </div>

                      <div class="principle-card flexibility">
                        <div class="principle-header">
                          <span class="principle-icon">🔧</span>
                          <h5>高灵活性</h5>
                        </div>
                        <div class="principle-content">
                          <p>提供显式API替代语言关键字，支持复杂场景</p>
                          <div class="principle-details">
                            <h6>功能特性</h6>
                            <ul>
                              <li>可中断的锁获取</li>
                              <li>公平/非公平锁选择</li>
                              <li>超时等待机制</li>
                              <li>多条件变量支持</li>
                            </ul>
                          </div>
                        </div>
                      </div>

                      <div class="principle-card composability">
                        <div class="principle-header">
                          <span class="principle-icon">🧩</span>
                          <h5>可组合性</h5>
                        </div>
                        <div class="principle-content">
                          <p>通过统一的抽象框架支持复杂并发模式</p>
                          <div class="principle-details">
                            <h6>设计模式</h6>
                            <ul>
                              <li>生产者-消费者模式</li>
                              <li>读写分离模式</li>
                              <li>Future/Promise模式</li>
                              <li>线程池化模式</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="juc-architecture">
                    <h4>🏛️ JUC架构全览</h4>
                    <div class="architecture-diagram">
                      <div class="arch-layer application">
                        <h5>应用层</h5>
                        <div class="arch-components">
                          <span class="component">Business Logic</span>
                          <span class="component">Service Layer</span>
                          <span class="component">Data Access</span>
                        </div>
                      </div>

                      <div class="arch-arrow">↓</div>

                      <div class="arch-layer juc-tools">
                        <h5>JUC工具层</h5>
                        <div class="arch-grid">
                          <div class="arch-group">
                            <h6>执行器</h6>
                            <span>Executor</span>
                            <span>ThreadPool</span>
                            <span>ForkJoin</span>
                          </div>
                          <div class="arch-group">
                            <h6>异步编程</h6>
                            <span>Future</span>
                            <span>CompletableFuture</span>
                            <span>CompletionService</span>
                          </div>
                          <div class="arch-group">
                            <h6>并发容器</h6>
                            <span>ConcurrentHashMap</span>
                            <span>CopyOnWriteArrayList</span>
                            <span>BlockingQueue</span>
                          </div>
                          <div class="arch-group">
                            <h6>同步器</h6>
                            <span>CountDownLatch</span>
                            <span>CyclicBarrier</span>
                            <span>Semaphore</span>
                          </div>
                        </div>
                      </div>

                      <div class="arch-arrow">↓</div>

                      <div class="arch-layer foundation">
                        <h5>基础层</h5>
                        <div class="arch-components">
                          <span class="component">AQS框架</span>
                          <span class="component">原子类</span>
                          <span class="component">Lock接口</span>
                          <span class="component">Condition</span>
                        </div>
                      </div>

                      <div class="arch-arrow">↓</div>

                      <div class="arch-layer hardware">
                        <h5>硬件层</h5>
                        <div class="arch-components">
                          <span class="component">CAS指令</span>
                          <span class="component">内存屏障</span>
                          <span class="component">缓存一致性</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 2: 原子类 -->
            <section id="topic-1" class="topic-section" ref="topic1">
              <ExpandableSection
                title="原子类 (Atomic classes)"
                :concept-data="atomicClassesData"
                @interaction="handleInteraction"
              >
                <div class="atomic-classes-showcase">
                  <h3>⚛️ 原子类：无锁并发的基石</h3>

                  <div class="atomic-motivation">
                    <h4>🎯 为什么需要原子类？</h4>
                    <div class="motivation-comparison">
                      <div class="comparison-item problem">
                        <div class="comparison-header">
                          <span class="comparison-icon">❌</span>
                          <h5>传统方案的困境</h5>
                        </div>
                        <div class="comparison-content">
                          <h6>volatile的局限性</h6>
                          <ul>
                            <li>只保证可见性，不保证原子性</li>
                            <li>i++ 这样的复合操作仍不安全</li>
                            <li>无法解决丢失更新问题</li>
                          </ul>
                          <h6>synchronized的开销</h6>
                          <ul>
                            <li>涉及操作系统互斥量，开销大</li>
                            <li>线程挂起和唤醒的额外成本</li>
                            <li>对于简单操作来说过于重量级</li>
                          </ul>
                        </div>
                      </div>

                      <div class="comparison-arrow">→</div>

                      <div class="comparison-item solution">
                        <div class="comparison-header">
                          <span class="comparison-icon">✅</span>
                          <h5>原子类的优势</h5>
                        </div>
                        <div class="comparison-content">
                          <h6>无锁并发</h6>
                          <ul>
                            <li>基于硬件CAS指令，性能优越</li>
                            <li>避免线程挂起，减少上下文切换</li>
                            <li>乐观锁思想，适合低竞争场景</li>
                          </ul>
                          <h6>原子性保证</h6>
                          <ul>
                            <li>复合操作的原子性执行</li>
                            <li>ABA问题的解决方案</li>
                            <li>丰富的原子操作API</li>
                          </ul>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Real-World Problems & Solutions for Atomic Classes -->
                  <div class="real-world-section">
                    <h4>🏭 项目实践踩坑与解决方案</h4>
                    <div class="problems-solutions">
                      <div class="problem-case">
                        <div class="case-header">
                          <span class="case-icon">⚠️</span>
                          <h5>常见问题：高并发计数器性能瓶颈</h5>
                        </div>
                        <div class="case-content">
                          <div class="problem-description">
                            <h6>问题描述</h6>
                            <p>
                              在实现高并发的全局性能指标计数器（如QPS统计、交易量统计）时，开发者初期使用
                              <code>AtomicLong</code
                              >。但在流量洪峰期，发现计数器所在的服务节点CPU使用率飙升至100%，业务吞吐量却上不去。
                            </p>
                          </div>

                          <div class="root-cause">
                            <h6>问题根源分析</h6>
                            <p>
                              这是典型的
                              <strong>CAS在极高竞争下的性能衰减</strong>
                              问题。成百上千个线程同时更新同一个
                              <code>AtomicLong</code>，导致绝大多数线程的
                              <code>compareAndSet</code> 操作失败，然后立即进入下一次
                              <code>for(;;)</code>
                              循环重试。这种密集的、无效的自旋操作疯狂消耗CPU周期，却很少有线程能成功推进，形成了事实上的"活锁"。
                            </p>
                          </div>

                          <div class="solutions">
                            <h6>业界主流解决方案与权衡</h6>

                            <div class="solution-option recommended">
                              <div class="solution-header">
                                <span class="solution-badge">推荐</span>
                                <h7>方案A: LongAdder (Java 8+ 首选)</h7>
                              </div>
                              <div class="solution-details">
                                <p>
                                  <strong>核心思想</strong>：空间换时间，分散热点。内部维护
                                  <code>Cell</code> 数组和
                                  <code>base</code> 字段，在低竞争时直接CAS更新
                                  <code>base</code>，竞争加剧时将线程哈希到不同
                                  <code>Cell</code> 进行更新。
                                </p>
                                <div class="pros-cons">
                                  <div class="pros">
                                    <h8>优点</h8>
                                    <ul>
                                      <li>高并发下吞吐量远超 AtomicLong</li>
                                      <li>CPU消耗显著降低</li>
                                      <li>官方优化，性能极致</li>
                                    </ul>
                                  </div>
                                  <div class="cons">
                                    <h8>缺点</h8>
                                    <ul>
                                      <li>
                                        <code>sum()</code> 成本比 <code>AtomicLong.get()</code> 略高
                                      </li>
                                      <li>返回的是"可能不完全精确的瞬时快照"</li>
                                      <li>内存占用稍大</li>
                                    </ul>
                                  </div>
                                </div>
                                <div class="trade-offs">
                                  <strong>权衡结论</strong>：对于高并发计数场景，<code
                                    >LongAdder</code
                                  >
                                  是当前业界无可争议的最佳实践。微小的精度损失在统计场景下完全可接受。
                                </div>
                              </div>
                            </div>

                            <div class="solution-option alternative">
                              <div class="solution-header">
                                <span class="solution-badge">备选</span>
                                <h7>方案B: 分段AtomicLong数组 (手动实现)</h7>
                              </div>
                              <div class="solution-details">
                                <p>
                                  <strong>核心思想</strong>：手动创建
                                  <code>AtomicLong</code> 数组，通过
                                  <code>Thread.currentThread().getId() % array.length</code>
                                  散列更新压力。
                                </p>
                                <div class="pros-cons">
                                  <div class="pros">
                                    <h8>优点</h8>
                                    <ul>
                                      <li>能有效缓解单点竞争</li>
                                      <li>原理与LongAdder类似</li>
                                      <li>可自定义散列策略</li>
                                    </ul>
                                  </div>
                                  <div class="cons">
                                    <h8>缺点</h8>
                                    <ul>
                                      <li>实现复杂度高</li>
                                      <li>可能存在伪共享问题</li>
                                      <li>性能优化不如官方极致</li>
                                    </ul>
                                  </div>
                                </div>
                                <div class="trade-offs">
                                  <strong>权衡结论</strong>：在
                                  <code>LongAdder</code>
                                  出现之前的历史解决方案，现在已无必要手动实现。
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="cas-principle">
                    <h4>🔄 CAS原理深度解析</h4>
                    <div class="cas-explanation">
                      <div class="cas-formula">
                        <h5>CAS操作三要素</h5>
                        <div class="formula-display">
                          <div class="cas-params">
                            <div class="param-item">
                              <span class="param-symbol">V</span>
                              <span class="param-desc">内存位置 (Variable)</span>
                            </div>
                            <div class="param-item">
                              <span class="param-symbol">A</span>
                              <span class="param-desc">预期值 (Expected)</span>
                            </div>
                            <div class="param-item">
                              <span class="param-symbol">B</span>
                              <span class="param-desc">新值 (New Value)</span>
                            </div>
                          </div>
                          <div class="cas-logic">
                            <code>if (V == A) { V = B; return true; } else { return false; }</code>
                          </div>
                        </div>
                      </div>

                      <div class="cas-demo">
                        <h5>🎭 CAS执行示例</h5>
                        <div class="demo-scenario">
                          <div class="scenario-step">
                            <div class="step-number">1</div>
                            <div class="step-content">
                              <h6>初始状态</h6>
                              <p>内存中的值 V = 100</p>
                              <div class="memory-visual">
                                <span class="memory-address">内存地址 0x1234</span>
                                <span class="memory-value">值: 100</span>
                              </div>
                            </div>
                          </div>

                          <div class="scenario-step">
                            <div class="step-number">2</div>
                            <div class="step-content">
                              <h6>线程A操作</h6>
                              <p>读取值100，计算101，准备CAS(100, 101)</p>
                              <div class="thread-action thread-a">
                                <span>Thread A: 期望100 → 设置101</span>
                              </div>
                            </div>
                          </div>

                          <div class="scenario-step">
                            <div class="step-number">3</div>
                            <div class="step-content">
                              <h6>线程B抢先</h6>
                              <p>线程B成功执行CAS(100, 200)</p>
                              <div class="thread-action thread-b">
                                <span>Thread B: CAS(100, 200) ✅ 成功</span>
                              </div>
                              <div class="memory-visual updated">
                                <span class="memory-address">内存地址 0x1234</span>
                                <span class="memory-value">值: 200</span>
                              </div>
                            </div>
                          </div>

                          <div class="scenario-step">
                            <div class="step-number">4</div>
                            <div class="step-content">
                              <h6>线程A重试</h6>
                              <p>CAS(100, 101)失败，重新读取200，计算201，执行CAS(200, 201)成功</p>
                              <div class="thread-action thread-a retry">
                                <span>Thread A: CAS(100, 101) ❌ 失败</span>
                                <span>Thread A: CAS(200, 201) ✅ 成功</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="atomic-types">
                    <h4>🧱 原子类型大全</h4>
                    <div class="types-grid">
                      <div class="type-category basic">
                        <div class="category-header">
                          <span class="category-icon">🔢</span>
                          <h5>基本原子类</h5>
                        </div>
                        <div class="type-list">
                          <div class="type-item">
                            <h6>AtomicInteger</h6>
                            <p>32位整数的原子操作</p>
                            <div class="api-examples">
                              <code>get(), set(), getAndIncrement()</code>
                              <code>compareAndSet(), addAndGet()</code>
                            </div>
                          </div>
                          <div class="type-item">
                            <h6>AtomicLong</h6>
                            <p>64位长整数的原子操作</p>
                            <div class="api-examples">
                              <code>incrementAndGet(), decrementAndGet()</code>
                              <code>getAndAdd(), updateAndGet()</code>
                            </div>
                          </div>
                          <div class="type-item">
                            <h6>AtomicBoolean</h6>
                            <p>布尔值的原子操作</p>
                            <div class="api-examples">
                              <code>compareAndSet(false, true)</code>
                              <code>getAndSet(), lazySet()</code>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="type-category reference">
                        <div class="category-header">
                          <span class="category-icon">📎</span>
                          <h5>引用原子类</h5>
                        </div>
                        <div class="type-list">
                          <div class="type-item">
                            <h6>AtomicReference&lt;T&gt;</h6>
                            <p>对象引用的原子操作</p>
                            <div class="api-examples">
                              <code>compareAndSet(expect, update)</code>
                              <code>getAndUpdate(Function)</code>
                            </div>
                          </div>
                          <div class="type-item">
                            <h6>AtomicStampedReference</h6>
                            <p>带版本戳的引用，解决ABA问题</p>
                            <div class="api-examples">
                              <code>compareAndSet(ref, newRef, stamp, newStamp)</code>
                            </div>
                          </div>
                          <div class="type-item">
                            <h6>AtomicMarkableReference</h6>
                            <p>带标记位的引用</p>
                            <div class="api-examples">
                              <code>compareAndSet(ref, newRef, mark, newMark)</code>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="type-category adder">
                        <div class="category-header">
                          <span class="category-icon">➕</span>
                          <h5>高性能累加器</h5>
                        </div>
                        <div class="type-list">
                          <div class="type-item highlight">
                            <h6>LongAdder</h6>
                            <p>高并发场景下的终极计数器</p>
                            <div class="api-examples">
                              <code>increment(), add(x), sum()</code>
                              <code>reset(), sumThenReset()</code>
                            </div>
                            <div class="performance-note">
                              <span class="perf-icon">🚀</span>
                              <span>高竞争时性能远超AtomicLong</span>
                            </div>
                          </div>
                          <div class="type-item">
                            <h6>DoubleAdder</h6>
                            <p>双精度浮点数累加器</p>
                            <div class="api-examples">
                              <code>add(x), sum(), reset()</code>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 3: 显式锁与条件 -->
            <section id="topic-2" class="topic-section" ref="topic2">
              <ExpandableSection
                title="显式锁与条件 (Lock classes & Condition objects)"
                :concept-data="lockConditionData"
                @interaction="handleInteraction"
              >
                <div class="lock-condition-showcase">
                  <h3>🔐 显式锁与条件：精确的线程协作</h3>

                  <div class="lock-evolution">
                    <h4>🚀 从synchronized到ReentrantLock的进化</h4>
                    <div class="evolution-comparison">
                      <div class="comparison-side traditional">
                        <div class="side-header">
                          <span class="side-icon">🔒</span>
                          <h5>synchronized (隐式锁)</h5>
                        </div>
                        <div class="side-content">
                          <div class="features">
                            <h6>特性</h6>
                            <ul>
                              <li>JVM内置，使用简单</li>
                              <li>自动获取和释放</li>
                              <li>支持重入</li>
                              <li>现代JVM优化良好</li>
                            </ul>
                          </div>
                          <div class="limitations">
                            <h6>局限性</h6>
                            <ul>
                              <li>无法中断等待锁的线程</li>
                              <li>无法尝试非阻塞获取锁</li>
                              <li>无法设置获取锁的超时</li>
                              <li>只支持非公平锁</li>
                              <li>wait/notify机制复杂易错</li>
                            </ul>
                          </div>
                        </div>
                      </div>

                      <div class="evolution-arrow">→</div>

                      <div class="comparison-side modern">
                        <div class="side-header">
                          <span class="side-icon">🗝️</span>
                          <h5>ReentrantLock (显式锁)</h5>
                        </div>
                        <div class="side-content">
                          <div class="features">
                            <h6>高级特性</h6>
                            <ul>
                              <li>可中断的锁获取 (lockInterruptibly)</li>
                              <li>尝试获取锁 (tryLock)</li>
                              <li>超时获取锁 (tryLock with timeout)</li>
                              <li>公平锁和非公平锁选择</li>
                              <li>多个Condition条件变量</li>
                            </ul>
                          </div>
                          <div class="responsibilities">
                            <h6>使用责任</h6>
                            <ul>
                              <li>必须手动释放锁</li>
                              <li>需要try-finally保证释放</li>
                              <li>更复杂的API学习成本</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Real-World Problems & Solutions for Locks -->
                  <div class="real-world-section">
                    <h4>🏭 项目实践踩坑与解决方案</h4>
                    <div class="problems-solutions">
                      <!-- 问题1：盲目替换synchronized -->
                      <div class="problem-case">
                        <div class="case-header">
                          <span class="case-icon">⚠️</span>
                          <h5>常见问题1：盲目替换synchronized导致性能下降</h5>
                        </div>
                        <div class="case-content">
                          <div class="problem-description">
                            <h6>问题描述</h6>
                            <p>
                              很多刚接触JUC的团队，会将代码中所有的
                              <code>synchronized</code> 简单地替换为
                              <code>ReentrantLock</code
                              >，期望获得性能提升，但结果往往事与愿违，有时甚至性能更差，并且代码变得更复杂。
                            </p>
                          </div>

                          <div class="root-cause">
                            <h6>问题根源分析</h6>
                            <p>
                              这种"一刀切"的做法忽略了
                              <code>synchronized</code>
                              在无竞争或低竞争场景下的性能优势。JVM的锁升级机制（偏向锁 → 轻量级锁 →
                              重量级锁）使得 <code>synchronized</code> 在大部分时间里开销极低。而
                              <code>ReentrantLock</code>
                              基于AQS，虽然功能强大，但其基础开销（如CAS操作、维护等待队列）相对较高。
                            </p>
                          </div>

                          <div class="solutions">
                            <h6>业界主流解决方案与权衡</h6>

                            <div class="solution-option recommended">
                              <div class="solution-header">
                                <span class="solution-badge">推荐</span>
                                <h7>方案A: 功能驱动替换</h7>
                              </div>
                              <div class="solution-details">
                                <p>
                                  <strong>核心原则</strong>：只在需要
                                  <code>ReentrantLock</code> 独有功能时才进行替换。
                                </p>
                                <div class="replacement-scenarios">
                                  <h8>替换场景</h8>
                                  <ul>
                                    <li>需要公平锁，保证等待最久的线程最先获取锁</li>
                                    <li>需要可中断的锁获取，避免线程无限期死等</li>
                                    <li>需要尝试获取锁，在获取不到锁时执行其他逻辑</li>
                                    <li>需要与Condition配合，实现复杂的等待/通知模式</li>
                                  </ul>
                                </div>
                                <div class="trade-offs">
                                  <strong>权衡结论</strong>：这是一种基于需求的务实策略，承认
                                  <code>synchronized</code> 的价值，把JUC
                                  Lock当作解决特定问题的"瑞士军刀"。
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- 问题2：多资源死锁 -->
                      <div class="problem-case">
                        <div class="case-header">
                          <span class="case-icon">💀</span>
                          <h5>常见问题2：多资源操作导致死锁</h5>
                        </div>
                        <div class="case-content">
                          <div class="problem-description">
                            <h6>问题描述</h6>
                            <p>
                              在构建需要同时操作多个资源的复杂业务时，例如银行转账（需同时锁定转出和转入两个账户），很容易因为加锁顺序不一致而导致<strong>死锁</strong>。线程1锁定账户A后尝试锁定账户B，同时线程2锁定账户B后尝试锁定账户A，两个线程互相等待，系统卡死。
                            </p>
                          </div>

                          <div class="root-cause">
                            <h6>问题根源分析</h6>
                            <p>
                              死锁的四个必要条件之一"持有并等待"（Hold and Wait）。<code
                                >synchronized</code
                              >
                              或
                              <code>lock()</code>
                              的阻塞特性使得线程在获取不到第二个锁时，并不会释放已经持有的第一个锁，从而导致了死锁循环。
                            </p>
                          </div>

                          <div class="solutions">
                            <h6>业界主流解决方案与权衡</h6>

                            <div class="solution-option recommended">
                              <div class="solution-header">
                                <span class="solution-badge">推荐</span>
                                <h7>方案A: tryLock + 失败回退</h7>
                              </div>
                              <div class="solution-details">
                                <p>
                                  <strong>核心思想</strong>：使用 <code>tryLock</code> 替代
                                  <code>lock()</code
                                  >，为两个锁的获取设置总超时时间。如果未能同时获得两个锁，就释放已持有的锁，随机等待后重试。
                                </p>
                                <div class="code-example">
                                  <pre><code>if (this.lock.tryLock()) {
    try {
        if (other.lock.tryLock()) {
            try {
                // ... 转账操作 ...
            } finally {
                other.lock.unlock();
            }
        }
    } finally {
        this.lock.unlock();
    }
}</code></pre>
                                </div>
                                <div class="pros-cons">
                                  <div class="pros">
                                    <h8>优点</h8>
                                    <ul>
                                      <li>通过主动释放已持有的锁，打破死锁必要条件</li>
                                      <li>非常健壮的防死锁策略</li>
                                      <li>适用于各种复杂场景</li>
                                    </ul>
                                  </div>
                                  <div class="cons">
                                    <h8>缺点</h8>
                                    <ul>
                                      <li>逻辑更复杂，需要管理重试和回退策略</li>
                                      <li>在无竞争时性能可能略低于直接lock()</li>
                                    </ul>
                                  </div>
                                </div>
                                <div class="trade-offs">
                                  <strong>权衡结论</strong
                                  >：通用的防御性编程模式，适合复杂的多资源操作场景。
                                </div>
                              </div>
                            </div>

                            <div class="solution-option alternative">
                              <div class="solution-header">
                                <span class="solution-badge">经典</span>
                                <h7>方案B: 全局锁序</h7>
                              </div>
                              <div class="solution-details">
                                <p>
                                  <strong>核心思想</strong
                                  >：强制规定一个全局的加锁顺序。例如，所有转账操作都必须先锁定账户ID较小的账户，再锁定ID较大的。
                                </p>
                                <div class="pros-cons">
                                  <div class="pros">
                                    <h8>优点</h8>
                                    <ul>
                                      <li>逻辑简单，实现清晰</li>
                                      <li>只要遵守约定，就能从根本上杜绝死锁</li>
                                      <li>性能开销最小</li>
                                    </ul>
                                  </div>
                                  <div class="cons">
                                    <h8>缺点</h8>
                                    <ul>
                                      <li>需要有明确的全局排序依据（如ID、时间戳）</li>
                                      <li>在动态、异构资源场景下可能难以实施</li>
                                    </ul>
                                  </div>
                                </div>
                                <div class="trade-offs">
                                  <strong>权衡结论</strong
                                  >：在资源有明确ID的情况下被广泛采用，是金融系统的首选方案。
                                </div>
                              </div>
                            </div>

                            <div class="solution-option alternative">
                              <div class="solution-header">
                                <span class="solution-badge">特化</span>
                                <h7>方案C: ReadWriteLock</h7>
                              </div>
                              <div class="solution-details">
                                <p>
                                  <strong>适用场景</strong
                                  >：如果场景是"读多写少"，例如查询账户信息远多于修改。使用
                                  <code>ReadWriteLock</code
                                  >，允许多个读线程同时持有读锁，但写线程必须独占写锁。
                                </p>
                                <div class="pros-cons">
                                  <div class="pros">
                                    <h8>优点</h8>
                                    <ul>
                                      <li>极大提升读操作的并发度</li>
                                      <li>提高系统整体吞吐量</li>
                                    </ul>
                                  </div>
                                  <div class="cons">
                                    <h8>缺点</h8>
                                    <ul>
                                      <li>增加锁的复杂性</li>
                                      <li>可能导致"写饥饿"问题</li>
                                    </ul>
                                  </div>
                                </div>
                                <div class="trade-offs">
                                  <strong>权衡结论</strong
                                  >：针对读多写少场景的特定优化，需要结合业务特点选择。
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="lock-deep-dive">
                    <h4>🔍 Lock接口深度解析</h4>
                    <div class="lock-explanation">
                      <div class="lock-definition">
                        <h5>💡 核心概念</h5>
                        <p>
                          <code>Lock</code> 是
                          <code>java.util.concurrent.locks</code> 包中定义的接口，提供比
                          <code>synchronized</code> 更广泛、更灵活的锁操作。<code
                            >ReentrantLock</code
                          >
                          是其最主要的实现类。
                        </p>
                        <div class="human-explanation">
                          <h6>人话版解释</h6>
                          <p>
                            <strong>Lock</strong>：可编程、可配置的"高级锁"，可以替代
                            <code>synchronized</code> 实现更复杂的锁定逻辑。
                          </p>
                          <p>
                            <strong>Condition</strong>：高级锁附带的"专用对讲机"。<code
                              >synchronized</code
                            >
                            只有一个公共广播频道，而一个 <code>Lock</code> 可以配多个
                            <code>Condition</code> 对讲机，精确唤醒特定频道的线程。
                          </p>
                        </div>
                      </div>

                      <div class="lock-pain-points">
                        <h5>🎯 解决synchronized的四大痛点</h5>
                        <div class="pain-points-grid">
                          <div class="pain-point">
                            <div class="pain-icon">🚫</div>
                            <div class="pain-content">
                              <h6>不可中断</h6>
                              <p>等待 <code>synchronized</code> 锁时不能被中断，只能死等</p>
                              <div class="solution">→ <code>lockInterruptibly()</code></div>
                            </div>
                          </div>
                          <div class="pain-point">
                            <div class="pain-icon">⚖️</div>
                            <div class="pain-content">
                              <h6>非公平性</h6>
                              <p>新线程可能比等待很久的线程先获得锁，导致"饥饿"</p>
                              <div class="solution">→ 公平锁选项</div>
                            </div>
                          </div>
                          <div class="pain-point">
                            <div class="pain-icon">🔧</div>
                            <div class="pain-content">
                              <h6>功能单一</h6>
                              <p>无法实现"尝试获取锁"或"限时等待锁"</p>
                              <div class="solution">→ <code>tryLock()</code></div>
                            </div>
                          </div>
                          <div class="pain-point">
                            <div class="pain-icon">📢</div>
                            <div class="pain-content">
                              <h6>单一等待队列</h6>
                              <p><code>notifyAll()</code> 唤醒所有线程，造成"惊群效应"</p>
                              <div class="solution">→ 多个 <code>Condition</code></div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="aqs-principle">
                        <h5>⚙️ AQS核心原理</h5>
                        <div class="aqs-explanation">
                          <p>
                            <code>ReentrantLock</code> 的核心是
                            <strong>AQS (AbstractQueuedSynchronizer)</strong>，它维护一个
                            <code>state</code> 变量和一个 <strong>CLH队列</strong> 来管理等待线程。
                          </p>
                          <div class="aqs-workflow">
                            <div class="workflow-item">
                              <div class="workflow-icon">🔒</div>
                              <div class="workflow-content">
                                <h6>lock()</h6>
                                <p>通过CAS更新state变量。失败则加入等待队列并挂起线程</p>
                              </div>
                            </div>
                            <div class="workflow-item">
                              <div class="workflow-icon">🔓</div>
                              <div class="workflow-content">
                                <h6>unlock()</h6>
                                <p>修改state变量，唤醒等待队列头部的下一个线程</p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="condition-mechanism">
                    <h4>🎯 Condition：精确的线程协作</h4>
                    <div class="condition-explanation">
                      <div class="condition-concept">
                        <h5>💡 核心概念</h5>
                        <p>
                          <code>Condition</code> 是与 <code>Lock</code> 关联的接口，提供类似于
                          <code>Object</code> 监视器方法的线程协作功能，但功能更强大、更具针对性。
                        </p>
                      </div>

                      <div class="condition-advantages">
                        <h5>🚀 相比wait/notify的优势</h5>
                        <div class="advantages-grid">
                          <div class="advantage-item">
                            <div class="advantage-icon">🎯</div>
                            <div class="advantage-content">
                              <h6>精确唤醒</h6>
                              <p>
                                可以创建多个Condition，针对不同条件精确唤醒特定线程，避免虚假唤醒
                              </p>
                            </div>
                          </div>
                          <div class="advantage-item">
                            <div class="advantage-icon">🔧</div>
                            <div class="advantage-content">
                              <h6>更强表达力</h6>
                              <p>await()、signal()、signalAll()语义更清晰，代码可读性更好</p>
                            </div>
                          </div>
                          <div class="advantage-item">
                            <div class="advantage-icon">⏰</div>
                            <div class="advantage-content">
                              <h6>超时支持</h6>
                              <p>支持带超时的等待，避免线程无限期阻塞</p>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="producer-consumer-example">
                        <h5>📋 经典案例：生产者-消费者模型</h5>
                        <div class="example-description">
                          <p>
                            一个有界队列，生产者在队列满时等待
                            <code>notFull</code> 条件，消费者在队列空时等待
                            <code>notEmpty</code> 条件。
                          </p>
                        </div>
                        <div class="code-example">
                          <pre><code>Lock lock = new ReentrantLock();
Condition notFull = lock.newCondition();
Condition notEmpty = lock.newCondition();

// 生产者
lock.lock();
try {
    while (count == items.length) {
        notFull.await(); // 队列满了，在"notFull"频道等待
    }
    // ... 入队操作 ...
    notEmpty.signal(); // 通知"notEmpty"频道，有新货了
} finally {
    lock.unlock();
}</code></pre>
                        </div>
                      </div>

                      <div class="lock-analogy">
                        <h5>🏢 生动类比：从普通门锁到智能门禁</h5>
                        <div class="analogy-comparison">
                          <div class="analogy-item traditional">
                            <div class="analogy-header">
                              <span class="analogy-icon">🚪</span>
                              <h6>synchronized = 普通会议室门锁</h6>
                            </div>
                            <div class="analogy-content">
                              <ul>
                                <li>只能从外面锁上，里面的人出来就自动开了</li>
                                <li>简单可靠，但功能有限</li>
                                <li>所有人都在同一个门外排队等待</li>
                              </ul>
                            </div>
                          </div>

                          <div class="analogy-item modern">
                            <div class="analogy-header">
                              <span class="analogy-icon">🔐</span>
                              <h6>ReentrantLock = 智能电子门禁</h6>
                            </div>
                            <div class="analogy-content">
                              <div class="lock-methods">
                                <div class="method-item">
                                  <code>lock()</code>
                                  <span>不停地尝试输入密码，直到门打开</span>
                                </div>
                                <div class="method-item">
                                  <code>tryLock()</code>
                                  <span>只试一次密码，不行就走开干别的</span>
                                </div>
                                <div class="method-item">
                                  <code>tryLock(time, unit)</code>
                                  <span>在限定时间内反复尝试，超时就放弃</span>
                                </div>
                                <div class="method-item">
                                  <code>lockInterruptibly()</code>
                                  <span>正在尝试时，如果手机响了可以立马停止去接电话</span>
                                </div>
                              </div>
                              <div class="condition-analogy">
                                <h7>Condition = 专用休息区</h7>
                                <p>
                                  高级会议室里有两个独立的休息区，一个给"销售团队"等，一个给"技术团队"等。你可以只通知"技术团队"进来开会，而"销售团队"继续在他们的休息区等待。
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="lock-comparison-table">
                        <h5>📊 synchronized vs ReentrantLock 详细对比</h5>
                        <div class="comparison-table">
                          <div class="table-header">
                            <div class="header-cell">特性</div>
                            <div class="header-cell">synchronized</div>
                            <div class="header-cell">ReentrantLock</div>
                          </div>

                          <div class="table-row">
                            <div class="cell feature-name">实现级别</div>
                            <div class="cell">JVM 内置关键字</div>
                            <div class="cell">JDK 库层面的 API</div>
                          </div>

                          <div class="table-row">
                            <div class="cell feature-name">锁释放</div>
                            <div class="cell">自动释放</div>
                            <div class="cell highlight-danger">必须手动释放（finally块）</div>
                          </div>

                          <div class="table-row">
                            <div class="cell feature-name">公平性</div>
                            <div class="cell">非公平</div>
                            <div class="cell highlight-success">可选（默认非公平）</div>
                          </div>

                          <div class="table-row">
                            <div class="cell feature-name">锁获取</div>
                            <div class="cell">阻塞，不可中断</div>
                            <div class="cell highlight-success">可中断、可超时、非阻塞</div>
                          </div>

                          <div class="table-row">
                            <div class="cell feature-name">线程协作</div>
                            <div class="cell">单一wait队列，notifyAll</div>
                            <div class="cell highlight-success">多个Condition，精确唤醒</div>
                          </div>

                          <div class="table-row">
                            <div class="cell feature-name">性能</div>
                            <div class="cell">锁竞争不激烈时，有锁升级优化</div>
                            <div class="cell">功能更强，但有固定开销</div>
                          </div>
                        </div>
                      </div>

                      <div class="common-pitfalls">
                        <h5>⚠️ 常见误区与注意事项</h5>
                        <div class="pitfalls-list">
                          <div class="pitfall-item critical">
                            <div class="pitfall-icon">💀</div>
                            <div class="pitfall-content">
                              <h6>首要天坑：忘记在finally块中unlock()</h6>
                              <p>
                                这是从synchronized迁移到Lock时最容易犯的、也是最致命的错误。一旦临界区代码抛出异常，锁将永远无法释放，导致所有其他等待该锁的线程全部死锁。
                              </p>
                            </div>
                          </div>
                          <div class="pitfall-item warning">
                            <div class="pitfall-icon">⚠️</div>
                            <div class="pitfall-content">
                              <h6>性能误区：认为ReentrantLock总是更快</h6>
                              <p>
                                不一定！需要根据实际场景和性能测试来决定。在低竞争场景下，synchronized可能性能更好。
                              </p>
                            </div>
                          </div>
                          <div class="pitfall-item info">
                            <div class="pitfall-icon">ℹ️</div>
                            <div class="pitfall-content">
                              <h6>使用注意：Lock与Condition的绑定关系</h6>
                              <p>
                                一个Lock实例可以创建多个Condition实例，但一个Condition实例只能从创建它的那个Lock实例中调用。不能混用。
                              </p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 4: 同步辅助工具 -->
            <section id="topic-3" class="topic-section" ref="topic3">
              <ExpandableSection
                title="同步辅助工具 (CountDownLatch & Synchronizers)"
                :concept-data="synchronizersData"
                @interaction="handleInteraction"
              >
                <div class="synchronizers-showcase">
                  <h3>🎯 同步辅助工具：线程协作的艺术</h3>

                  <div class="synchronizers-overview">
                    <h4>🧩 同步器家族全览</h4>
                    <div class="synchronizers-grid">
                      <div class="synchronizer-card countdown">
                        <div class="card-header">
                          <span class="card-icon">⏳</span>
                          <h5>CountDownLatch</h5>
                          <span class="card-badge">一次性</span>
                        </div>
                        <div class="card-content">
                          <p><strong>核心思想</strong>：倒计时门闩，等待N个事件完成</p>
                          <div class="use-cases">
                            <h6>典型场景</h6>
                            <ul>
                              <li>主线程等待多个工作线程完成初始化</li>
                              <li>并行任务的汇总点</li>
                              <li>服务启动协调</li>
                            </ul>
                          </div>
                          <div class="key-methods">
                            <h6>关键方法</h6>
                            <code>countDown()</code> <code>await()</code>
                          </div>
                        </div>
                      </div>

                      <div class="synchronizer-card barrier">
                        <div class="card-header">
                          <span class="card-icon">🚧</span>
                          <h5>CyclicBarrier</h5>
                          <span class="card-badge">可重用</span>
                        </div>
                        <div class="card-content">
                          <p><strong>核心思想</strong>：循环屏障，等待所有线程到达同步点</p>
                          <div class="use-cases">
                            <h6>典型场景</h6>
                            <ul>
                              <li>多线程分阶段计算</li>
                              <li>并行算法的同步点</li>
                              <li>游戏中的回合制同步</li>
                            </ul>
                          </div>
                          <div class="key-methods">
                            <h6>关键方法</h6>
                            <code>await()</code> <code>reset()</code>
                          </div>
                        </div>
                      </div>

                      <div class="synchronizer-card semaphore">
                        <div class="card-header">
                          <span class="card-icon">🎫</span>
                          <h5>Semaphore</h5>
                          <span class="card-badge">计数型</span>
                        </div>
                        <div class="card-content">
                          <p><strong>核心思想</strong>：信号量，控制同时访问资源的线程数量</p>
                          <div class="use-cases">
                            <h6>典型场景</h6>
                            <ul>
                              <li>连接池大小限制</li>
                              <li>限流器实现</li>
                              <li>资源访问控制</li>
                            </ul>
                          </div>
                          <div class="key-methods">
                            <h6>关键方法</h6>
                            <code>acquire()</code> <code>release()</code>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Real-World Problems & Solutions for Synchronizers -->
                  <div class="real-world-section">
                    <h4>🏭 项目实践踩坑与解决方案</h4>
                    <div class="problems-solutions">
                      <div class="problem-case">
                        <div class="case-header">
                          <span class="case-icon">⚠️</span>
                          <h5>常见问题：CountDownLatch使用不当导致死锁</h5>
                        </div>
                        <div class="case-content">
                          <div class="problem-description">
                            <h6>问题描述</h6>
                            <p>
                              在微服务启动场景中，主线程使用
                              <code>CountDownLatch</code>
                              等待多个组件初始化完成。但某个组件初始化失败抛异常，没有调用
                              <code>countDown()</code>，导致主线程永远阻塞在
                              <code>await()</code>，整个服务无法启动。
                            </p>
                          </div>

                          <div class="root-cause">
                            <h6>问题根源分析</h6>
                            <p>
                              <code>CountDownLatch</code>
                              是一次性的，计数器不会自动重置。如果某个线程异常退出而没有调用
                              <code>countDown()</code
                              >，等待的线程将永远阻塞。这在异常处理不完善的代码中很容易发生。
                            </p>
                          </div>

                          <div class="solutions">
                            <h6>业界主流解决方案与权衡</h6>

                            <div class="solution-option recommended">
                              <div class="solution-header">
                                <span class="solution-badge">推荐</span>
                                <h7>方案A: try-finally保证countDown</h7>
                              </div>
                              <div class="solution-details">
                                <p>
                                  <strong>核心思想</strong
                                  >：无论初始化成功还是失败，都要在finally块中调用
                                  <code>countDown()</code>。
                                </p>
                                <div class="code-example">
                                  <pre><code>// 工作线程中
try {
    // 执行初始化逻辑
    initializeComponent();
} finally {
    // 无论成功失败都要countDown
    latch.countDown();
}</code></pre>
                                </div>
                                <div class="trade-offs">
                                  <strong>权衡结论</strong
                                  >：简单可靠，是最基础的防护措施。但需要在业务逻辑中区分初始化是否真正成功。
                                </div>
                              </div>
                            </div>

                            <div class="solution-option alternative">
                              <div class="solution-header">
                                <span class="solution-badge">备选</span>
                                <h7>方案B: 带超时的await</h7>
                              </div>
                              <div class="solution-details">
                                <p>
                                  <strong>核心思想</strong>：使用
                                  <code>await(timeout, TimeUnit)</code> 避免无限期等待。
                                </p>
                                <div class="code-example">
                                  <pre><code>// 主线程中
if (!latch.await(30, TimeUnit.SECONDS)) {
    // 超时处理逻辑
    throw new TimeoutException("组件初始化超时");
}</code></pre>
                                </div>
                                <div class="trade-offs">
                                  <strong>权衡结论</strong
                                  >：提供了兜底保护，但需要合理设置超时时间，过短可能误判，过长可能影响故障恢复速度。
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- 问题2：数据迁移服务案例 -->
                      <div class="problem-case">
                        <div class="case-header">
                          <span class="case-icon">📊</span>
                          <h5>经典案例：数据迁移服务的任务分发-结果汇总</h5>
                        </div>
                        <div class="case-content">
                          <div class="problem-description">
                            <h6>场景描述</h6>
                            <p>
                              一个数据迁移服务，主线程负责将一个巨大的CSV文件分割成100个小文件，然后将这100个处理任务提交到线程池中并行执行，期望将数据插入数据库。主线程必须等待所有100个任务都成功完成后，才能更新总任务状态为"成功"并释放相关资源。
                            </p>
                          </div>

                          <div class="root-cause">
                            <h6>问题根源分析</h6>
                            <p>
                              这是一个典型的"任务分发-结果汇总"模型，核心是主线程如何高效、可靠地等待所有并行子任务完成。
                            </p>
                          </div>

                          <div class="solutions">
                            <h6>业界主流解决方案与权衡</h6>

                            <div class="solution-option recommended">
                              <div class="solution-header">
                                <span class="solution-badge">最佳</span>
                                <h7>方案A: 使用CountDownLatch</h7>
                              </div>
                              <div class="solution-details">
                                <p><strong>实现步骤</strong>：这是该场景下的教科书式解决方案。</p>
                                <div class="implementation-steps">
                                  <div class="step-item">
                                    <span class="step-num">1</span>
                                    <span
                                      ><code
                                        >CountDownLatch latch = new CountDownLatch(100);</code
                                      ></span
                                    >
                                  </div>
                                  <div class="step-item">
                                    <span class="step-num">2</span>
                                    <span
                                      >主线程循环创建100个Runnable任务，每个任务都持有latch的引用，并提交到线程池</span
                                    >
                                  </div>
                                  <div class="step-item">
                                    <span class="step-num">3</span>
                                    <span>主线程调用 <code>latch.await();</code> 进入等待</span>
                                  </div>
                                  <div class="step-item">
                                    <span class="step-num">4</span>
                                    <span
                                      >每个Runnable的run方法中，在try...finally块的finally子句中调用
                                      <code>latch.countDown();</code></span
                                    >
                                  </div>
                                </div>
                                <div class="code-example">
                                  <pre><code>CountDownLatch latch = new CountDownLatch(100);

// 提交任务
for (int i = 0; i < 100; i++) {
    executor.submit(() -> {
        try {
            // 处理数据迁移任务
            processDataMigration();
        } finally {
            // 确保无论成功还是异常都会countDown
            latch.countDown();
        }
    });
}

// 等待所有任务完成
latch.await();
updateTaskStatus("SUCCESS");</code></pre>
                                </div>
                                <div class="trade-offs">
                                  <strong>权衡结论</strong
                                  >：该方案逻辑清晰、实现简单、性能高效，是解决此类问题的最佳实践。它完美地将"任务完成"这一事件与线程本身解耦。
                                </div>
                              </div>
                            </div>

                            <div class="solution-option alternative">
                              <div class="solution-header">
                                <span class="solution-badge">备选</span>
                                <h7>方案B: Future + get()循环</h7>
                              </div>
                              <div class="solution-details">
                                <p>
                                  <strong>核心思想</strong>：主线程通过
                                  <code>ExecutorService.submit()</code> 提交任务并获得
                                  <code>List&lt;Future&lt;?&gt;&gt;</code>，然后循环遍历调用
                                  <code>get()</code> 方法。
                                </p>
                                <div class="pros-cons">
                                  <div class="pros">
                                    <h8>优点</h8>
                                    <ul>
                                      <li>可以处理每个任务的返回值</li>
                                      <li>可以捕获每个任务抛出的异常</li>
                                    </ul>
                                  </div>
                                  <div class="cons">
                                    <h8>缺点</h8>
                                    <ul>
                                      <li>逻辑上是"串行"等待，按顺序等待每个Future</li>
                                      <li>不如CountDownLatch能清晰表达"只关心全部完成"的意图</li>
                                    </ul>
                                  </div>
                                </div>
                                <div class="trade-offs">
                                  <strong>权衡结论</strong
                                  >：如果只关心"全部完成"这个事件，CountDownLatch是不二之选。如果还需要处理每个子任务的返回结果，则应使用
                                  <code>ExecutorCompletionService</code> 或
                                  <code>CompletableFuture.allOf()</code>。
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="countdown-latch-deep-dive">
                    <h4>⏳ CountDownLatch深度解析</h4>
                    <div class="countdown-explanation">
                      <div class="countdown-definition">
                        <h5>💡 核心概念</h5>
                        <p>
                          <code>CountDownLatch</code>
                          是一个同步辅助类，它允许一个或多个线程一直等待，直到其他线程的操作执行完毕。
                        </p>
                        <div class="human-explanation">
                          <h6>人话版解释</h6>
                          <p>
                            它就像一个"倒数计数器"。你用一个初始数字（比如10）来设置它，然后把它分发给多个线程。主线程调用
                            <code>await()</code>
                            方法就会被阻塞。其他每个线程完成自己的任务后，就调用
                            <code>countDown()</code> 方法，计数器减1。当计数器减到0时，所有在
                            <code>await()</code> 上等待的线程就会被唤醒，继续执行。
                          </p>
                        </div>
                      </div>

                      <div class="countdown-principle">
                        <h5>⚙️ 核心原理</h5>
                        <div class="principle-explanation">
                          <p>
                            <strong>存在价值</strong
                            >：为了解决"等待N个事件完成"这一类常见的并发协调问题。如果使用
                            <code>Thread.join()</code
                            >，主线程必须持有所有子线程的引用，不够灵活。如果用
                            <code>Lock/Condition</code>，则过于笨重。
                          </p>
                          <div class="aqs-implementation">
                            <h6>AQS实现机制</h6>
                            <ul>
                              <li><strong>共享模式</strong>：基于AQS的共享模式实现</li>
                              <li><strong>state变量</strong>：count就是AQS的state</li>
                              <li>
                                <strong>await()机制</strong
                                >：检查state是否为0，不为0则线程进入等待队列
                              </li>
                              <li>
                                <strong>countDown()机制</strong
                                >：通过CAS循环原子性地将state减1，变为0时唤醒所有等待线程
                              </li>
                            </ul>
                          </div>
                          <div class="key-characteristics">
                            <h6>关键特性</h6>
                            <div class="characteristic-item">
                              <span class="characteristic-icon">🔒</span>
                              <span
                                ><strong>一次性</strong
                                >：计数器无法重置，一旦计数归零，后续await()调用立即返回</span
                              >
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="countdown-analogy">
                        <h5>🏃‍♂️ 生动类比：田径比赛的终点裁判</h5>
                        <div class="analogy-scenario">
                          <div class="analogy-step">
                            <div class="step-icon">🏁</div>
                            <div class="step-content">
                              <h6>new CountDownLatch(N)</h6>
                              <p>裁判知道有N个选手参赛</p>
                            </div>
                          </div>
                          <div class="analogy-step">
                            <div class="step-icon">⏳</div>
                            <div class="step-content">
                              <h6>latch.await()</h6>
                              <p>终点线的裁判在原地等待，直到所有选手都冲过终点</p>
                            </div>
                          </div>
                          <div class="analogy-step">
                            <div class="step-icon">👋</div>
                            <div class="step-content">
                              <h6>latch.countDown()</h6>
                              <p>每当一个选手冲过终点，他就向裁判挥手示意</p>
                            </div>
                          </div>
                          <div class="analogy-step">
                            <div class="step-icon">🎉</div>
                            <div class="step-content">
                              <h6>计数归零</h6>
                              <p>当裁判看到N个选手都挥手了，他就知道比赛结束了，可以去统计成绩了</p>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="countdown-comparison">
                        <h5>⚖️ 横向对比</h5>
                        <div class="comparison-grid">
                          <div class="comparison-item">
                            <h6>vs CyclicBarrier</h6>
                            <div class="comparison-details">
                              <div class="diff-point">
                                <span class="diff-label">CountDownLatch</span>
                                <span>计数器只能用一次，一组线程等待另一组线程</span>
                              </div>
                              <div class="diff-point">
                                <span class="diff-label">CyclicBarrier</span>
                                <span>计数器可重置，一组线程互相等待，适用于分阶段任务</span>
                              </div>
                            </div>
                          </div>
                          <div class="comparison-item">
                            <h6>vs Thread.join()</h6>
                            <div class="comparison-details">
                              <div class="diff-point">
                                <span class="diff-label">join()</span>
                                <span>必须等待指定线程终止</span>
                              </div>
                              <div class="diff-point">
                                <span class="diff-label">CountDownLatch</span>
                                <span>不关心线程本身，只关心任务是否完成，更加灵活</span>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="countdown-workflow">
                        <h5>🔄 工作流程</h5>
                        <div class="workflow-steps">
                          <div class="workflow-step">
                            <div class="step-number">1</div>
                            <div class="step-content">
                              <h6>初始化</h6>
                              <p>创建CountDownLatch(n)，内部计数器设为n</p>
                            </div>
                          </div>
                          <div class="workflow-step">
                            <div class="step-number">2</div>
                            <div class="step-content">
                              <h6>工作线程</h6>
                              <p>完成任务后调用countDown()，计数器减1</p>
                            </div>
                          </div>
                          <div class="workflow-step">
                            <div class="step-number">3</div>
                            <div class="step-content">
                              <h6>等待线程</h6>
                              <p>调用await()阻塞，直到计数器归零</p>
                            </div>
                          </div>
                          <div class="workflow-step">
                            <div class="step-number">4</div>
                            <div class="step-content">
                              <h6>全部唤醒</h6>
                              <p>计数器归零时，所有等待线程被唤醒</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 5: 并发容器 -->
            <section id="topic-4" class="topic-section" ref="topic4">
              <ExpandableSection
                title="并发容器 (Concurrent containers)"
                :concept-data="concurrentContainersData"
                @interaction="handleInteraction"
              >
                <div class="concurrent-containers-showcase">
                  <h3>📦 并发容器：高性能数据结构的艺术</h3>

                  <div class="containers-overview">
                    <h4>🏗️ 并发容器家族</h4>
                    <div class="containers-grid">
                      <div class="container-card concurrent-map">
                        <div class="card-header">
                          <span class="card-icon">🗺️</span>
                          <h5>ConcurrentHashMap</h5>
                          <span class="card-badge">高并发读写</span>
                        </div>
                        <div class="card-content">
                          <div class="concept-definition">
                            <h6>💡 核心概念</h6>
                            <p>
                              <code>ConcurrentHashMap</code>
                              是一个线程安全且支持高并发读写的哈希表，是JUC提供的高性能
                              <code>Map</code> 实现。
                            </p>
                            <div class="human-explanation">
                              <strong>人话版解释</strong>：一个可以在多线程环境下放心使用的
                              <code>HashMap</code>。它既保证了线程安全，又提供了比老旧的
                              <code>Hashtable</code> 好得多的性能。
                            </div>
                          </div>

                          <div class="design-principle">
                            <h6>🎯 设计原理</h6>
                            <p>
                              <strong>存在价值</strong>：<code>HashMap</code>
                              线程不安全，并发put可能导致数据丢失，甚至在扩容时形成环形链表使get操作陷入死循环。而
                              <code>Hashtable</code> 在每个方法上都加
                              <code>synchronized</code>，相当于给整个Map加了一把大锁，性能极差。
                            </p>
                            <p>
                              <strong>核心原理</strong>：<strong>锁分段技术 (Lock Striping)</strong
                              >。放弃对整个Map加一把大锁，而是将其内部分割成多个独立的"段"，每个段都有自己的锁，实现真正的并行访问。
                            </p>
                          </div>

                          <div class="evolution-timeline">
                            <h6>🚀 版本演进</h6>
                            <div class="timeline-item java7">
                              <div class="timeline-header">
                                <span class="version-badge">Java 7</span>
                                <h7>分段锁 (Segment)</h7>
                              </div>
                              <div class="timeline-content">
                                <p><strong>结构</strong>：Segment数组 + HashEntry链表</p>
                                <p>
                                  <strong>机制</strong
                                  >：Segment继承自ReentrantLock，默认16个Segment，理论上最多支持16个线程并发写入
                                </p>
                              </div>
                            </div>
                            <div class="timeline-item java8">
                              <div class="timeline-header">
                                <span class="version-badge">Java 8+</span>
                                <h7>CAS + Node锁</h7>
                              </div>
                              <div class="timeline-content">
                                <p><strong>结构</strong>：数组 + 链表/红黑树</p>
                                <p>
                                  <strong>机制</strong
                                  >：直接在数组的每个头结点上进行锁定（使用synchronized），锁粒度与bucket数量相同，并发度大大提高
                                </p>
                                <p><strong>优化</strong>：链表长度超过阈值时转化为红黑树</p>
                              </div>
                            </div>
                          </div>

                          <div class="performance-highlights">
                            <h6>⚡ 性能特点</h6>
                            <ul>
                              <li>读操作几乎无锁</li>
                              <li>写操作精细化锁定</li>
                              <li>扩容时渐进式迁移</li>
                            </ul>
                          </div>

                          <div class="bank-analogy">
                            <h6>🏦 生动类比：银行窗口服务</h6>
                            <div class="analogy-comparison">
                              <div class="analogy-item">
                                <strong>Hashtable</strong
                                >：只有一个窗口的银行大厅，所有客户都必须在一个窗口排队
                              </div>
                              <div class="analogy-item">
                                <strong>ConcurrentHashMap (Java 8)</strong
                                >：有几百个窗口的大型银行，每个客户根据自己的业务号码（哈希值）去指定的窗口，只要窗口不同，大家就可以并行办理业务
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div class="container-card copy-on-write">
                        <div class="card-header">
                          <span class="card-icon">📋</span>
                          <h5>CopyOnWriteArrayList</h5>
                          <span class="card-badge">读多写少</span>
                        </div>
                        <div class="card-content">
                          <div class="cow-definition">
                            <h6>💡 核心概念</h6>
                            <p>
                              <code>CopyOnWriteArrayList</code> (简称COW列表) 是
                              <code>List</code>
                              接口的一个线程安全的变体，它通过在每次进行修改（<code>add</code>,
                              <code>set</code> 等）时，都创建一个底层数组的全新副本来实现线程安全。
                            </p>
                            <div class="human-explanation">
                              <strong>人话版解释</strong
                              >：一个"写操作极度奢侈"的列表。当你要修改它时，它不是在原地改，而是先把所有内容复制一份，在新副本上修改，然后把引用指向新副本。而所有的读操作，都是在当前时刻的那个（不变的）旧副本上进行的。
                            </div>
                          </div>

                          <div class="cow-principle">
                            <h6>🎯 设计原理</h6>
                            <p>
                              <strong>存在价值</strong
                              >：为了应对<strong>"读多写少"</strong>的并发场景。在这些场景中，读操作的频率远高于写操作。如果使用
                              <code>synchronized</code> 或
                              <code>ReentrantLock</code
                              >，即使是读操作也需要加锁，当大量读操作并发时，锁的争用会成为性能瓶颈。
                            </p>
                            <p>
                              <strong>核心原理</strong>：<strong
                                >不变性 (Immutability) 与写时复制 (Copy-On-Write)</strong
                              >
                            </p>
                            <div class="principle-details">
                              <div class="principle-item">
                                <span class="principle-num">1</span>
                                <span
                                  >内部数据由一个 <code>volatile Object[]</code> 数组维护，<code
                                    >volatile</code
                                  >
                                  保证引用指向新数组时所有线程都能立刻看到</span
                                >
                              </div>
                              <div class="principle-item">
                                <span class="principle-num">2</span>
                                <span
                                  >读操作（如 <code>get</code>,
                                  <code>iterator</code>）直接访问这个数组，不加任何锁</span
                                >
                              </div>
                              <div class="principle-item">
                                <span class="principle-num">3</span>
                                <span
                                  >写操作在
                                  <code>ReentrantLock</code>
                                  保护下进行：复制现有数组→在新数组上修改→将内部引用指向新数组</span
                                >
                              </div>
                            </div>
                          </div>

                          <div class="cow-analogy">
                            <h6>📰 生动类比：微信公众号发布文章</h6>
                            <div class="analogy-scenario">
                              <div class="analogy-step">
                                <div class="step-icon">👥</div>
                                <div class="step-content">
                                  <h7>读操作</h7>
                                  <p>
                                    成千上万的读者在阅读一篇文章，他们互不干扰，也不需要任何许可（无锁）
                                  </p>
                                </div>
                              </div>
                              <div class="analogy-step">
                                <div class="step-icon">✏️</div>
                                <div class="step-content">
                                  <h7>写操作</h7>
                                  <p>
                                    当作者想修改文章时（比如改个错别字），他不能在已发布的文章上直接改。他必须进入后台，复制一份草稿，在草稿上修改完毕后，点击"发布"，系统会用这篇新的文章替换掉旧的
                                  </p>
                                </div>
                              </div>
                              <div class="analogy-step">
                                <div class="step-icon">🔄</div>
                                <div class="step-content">
                                  <h7>一致性保证</h7>
                                  <p>
                                    正在阅读旧文章的读者，可以继续读完，不受任何影响。之后来的新读者，将看到修改后的新文章
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>

                          <div class="cow-mechanism">
                            <h6>⚙️ 工作机制</h6>
                            <div class="mechanism-steps">
                              <div class="step">
                                <span class="step-num">1</span>
                                <span>读操作直接访问数组，无需加锁</span>
                              </div>
                              <div class="step">
                                <span class="step-num">2</span>
                                <span>写操作复制整个数组</span>
                              </div>
                              <div class="step">
                                <span class="step-num">3</span>
                                <span>在副本上修改，然后原子替换引用</span>
                              </div>
                            </div>
                          </div>

                          <div class="cow-characteristics">
                            <h6>🔍 关键特性</h6>
                            <div class="characteristics-grid">
                              <div class="characteristic">
                                <span class="char-icon">🔒</span>
                                <div class="char-content">
                                  <h7>迭代安全</h7>
                                  <p>迭代器绝对安全，不会抛出ConcurrentModificationException</p>
                                </div>
                              </div>
                              <div class="characteristic">
                                <span class="char-icon">📊</span>
                                <div class="char-content">
                                  <h7>最终一致性</h7>
                                  <p>只能保证数据的最终一致性，而不是实时一致性</p>
                                </div>
                              </div>
                            </div>
                          </div>

                          <div class="trade-offs">
                            <h6>📋 适用场景</h6>
                            <ul>
                              <li>读操作远多于写操作</li>
                              <li>迭代器需要安全遍历</li>
                              <li>可以容忍写操作的高成本</li>
                              <li><strong>经典场景</strong>：事件监听器模式</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- Real-World Problems & Solutions for Concurrent Containers -->
                  <div class="real-world-section">
                    <h4>🏭 项目实践踩坑与解决方案</h4>
                    <div class="problems-solutions">
                      <div class="problem-case">
                        <div class="case-header">
                          <span class="case-icon">⚠️</span>
                          <h5>经典案例：事件监听器性能瓶颈的完美解决</h5>
                        </div>
                        <div class="case-content">
                          <div class="problem-description">
                            <h6>问题描述</h6>
                            <p>
                              一个系统中广泛使用的设计模式是"事件监听器"。一个事件源可以注册（<code>add</code>）多个监听器（<code>Listener</code>），当事件发生时，会遍历所有已注册的监听器并调用其处理方法。在早期设计中，这个监听器列表使用了
                              <code>ArrayList</code> 并配合
                              <code>synchronized</code>
                              块进行保护。在高并发下，发现事件处理的吞吐量上不去，线程堆栈显示大量线程阻塞在获取这个列表的锁上。
                            </p>
                          </div>

                          <div class="root-cause">
                            <h6>问题根源分析</h6>
                            <p>
                              监听器的注册和注销（写操作）非常少，通常只在系统启动和关闭时发生。但事件的触发和通知（读操作）却极其频繁。<code
                                >synchronized</code
                              >
                              导致了所有无状态的读操作也必须排队获取锁，造成了严重的锁竞争。
                            </p>
                          </div>

                          <div class="solutions">
                            <h6>业界主流解决方案与权衡</h6>

                            <div class="solution-option recommended">
                              <div class="solution-header">
                                <span class="solution-badge">最佳</span>
                                <h7>使用CopyOnWriteArrayList替换synchronizedList</h7>
                              </div>
                              <div class="solution-details">
                                <p>
                                  <strong>核心思想</strong
                                  >：利用CopyOnWriteArrayList的读操作无锁特性，完美匹配事件监听器的"读多写少"模式。
                                </p>
                                <div class="pros-cons">
                                  <div class="pros">
                                    <h8>优点</h8>
                                    <ul>
                                      <li>
                                        <strong>读操作无锁</strong
                                        >：事件通知（遍历列表）的性能得到了极大的提升
                                      </li>
                                      <li><strong>线程安全</strong>：监听器的增删是安全的</li>
                                      <li>
                                        <strong>迭代安全</strong
                                        >：在遍历监听器列表的同时，可以安全地添加或删除监听器，不会有ConcurrentModificationException
                                      </li>
                                    </ul>
                                  </div>
                                  <div class="cons">
                                    <h8>缺点</h8>
                                    <ul>
                                      <li>注册和注销监听器的成本变高了</li>
                                      <li>内存占用略有增加</li>
                                    </ul>
                                  </div>
                                </div>
                                <div class="trade-offs">
                                  <strong>权衡结论</strong
                                  >：在这个典型的"读远大于写"的监听器/发布-订阅模式中，写操作的高成本被完全摊薄了。用一个可接受的、偶发的写成本，换取了海量的、高频的读性能提升，这个权衡是非常值得的。
                                </div>
                                <div class="industry-choice">
                                  <h8>🏆 业界选择</h8>
                                  <p>
                                    <strong
                                      >事件监听器模式是CopyOnWriteArrayList的经典、也是最主要的应用场景</strong
                                    >。几乎所有知名的框架（如Spring）中的事件多播器都是基于这个原理实现的。
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- 问题2：HashMap并发问题 -->
                      <div class="problem-case">
                        <div class="case-header">
                          <span class="case-icon">💀</span>
                          <h5>致命问题：HashMap并发导致CPU 100%死循环</h5>
                        </div>
                        <div class="case-content">
                          <div class="problem-description">
                            <h6>问题描述</h6>
                            <p>
                              在一个高并发服务中，使用了一个全局的
                              <code>HashMap</code>
                              作为缓存。在压测或线上高流量时，服务偶尔会完全夯住，CPU
                              100%，通过线程堆栈分析发现，大量线程卡在
                              <code>HashMap.get()</code>
                              方法上，形成了一个<strong>无限循环</strong>。
                            </p>
                          </div>

                          <div class="root-cause">
                            <h6>问题根源分析</h6>
                            <p>
                              这是
                              <code>HashMap</code>
                              在并发扩容（<code>resize</code>）时，由于没有同步保护，多个线程同时修改内部链表结构，导致链表形成了一个环。当后续有线程
                              <code>get</code>
                              一个位于这个环形链表中的key时，就会陷入无限循环，耗尽CPU。
                            </p>
                            <div class="danger-illustration">
                              <h7>🔥 危险场景示意</h7>
                              <div class="scenario-steps">
                                <div class="scenario-step">
                                  <span class="step-icon">1️⃣</span>
                                  <span>线程A开始扩容，读取旧链表</span>
                                </div>
                                <div class="scenario-step">
                                  <span class="step-icon">2️⃣</span>
                                  <span>线程B同时扩容，修改链表指针</span>
                                </div>
                                <div class="scenario-step">
                                  <span class="step-icon">3️⃣</span>
                                  <span>线程A继续操作，形成环形链表</span>
                                </div>
                                <div class="scenario-step">
                                  <span class="step-icon">💀</span>
                                  <span>后续get操作陷入无限循环</span>
                                </div>
                              </div>
                            </div>
                          </div>

                          <div class="solutions">
                            <h6>业界主流解决方案与权衡</h6>

                            <div class="solution-option recommended">
                              <div class="solution-header">
                                <span class="solution-badge">唯一正解</span>
                                <h7>直接替换为ConcurrentHashMap</h7>
                              </div>
                              <div class="solution-details">
                                <p>
                                  <strong>核心原则</strong>：这是解决
                                  <code>HashMap</code> 并发问题的标准、也是唯一的正确答案。
                                </p>
                                <div class="pros-cons">
                                  <div class="pros">
                                    <h8>优点</h8>
                                    <ul>
                                      <li>
                                        <strong>线程安全</strong
                                        >：从根本上解决了并发修改导致的数据结构损坏问题
                                      </li>
                                      <li>
                                        <strong>高性能</strong
                                        >：通过精细的锁分段或节点锁技术，提供了卓越的并发性能
                                      </li>
                                      <li>
                                        <strong>无缝替换</strong>：对于大部分场景，替换工作只是
                                        <code>new HashMap&lt;&gt;()</code> →
                                        <code>new ConcurrentHashMap&lt;&gt;()</code>
                                      </li>
                                    </ul>
                                  </div>
                                  <div class="cons">
                                    <h8>缺点</h8>
                                    <ul>
                                      <li>比HashMap占用略多的内存</li>
                                      <li>在无任何竞争的单线程环境下，性能略低于HashMap</li>
                                    </ul>
                                  </div>
                                </div>
                                <div class="trade-offs">
                                  <strong>权衡结论</strong
                                  >：这点微不足道的性能和内存开销，与它所解决的并发安全问题相比，完全可以忽略不计。
                                </div>
                                <div class="industry-choice">
                                  <h8>🏆 业界选择</h8>
                                  <p>
                                    在任何可能存在多线程访问的Map场景下，<strong>都应该使用ConcurrentHashMap</strong>，而不是自己尝试去对HashMap加锁。这已经是Java开发的铁律。只有在能100%确定该Map只会在单线程内访问的局部变量等场景下，才使用HashMap。
                                  </p>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 6: 阻塞队列 -->
            <section id="topic-5" class="topic-section" ref="topic5">
              <ExpandableSection
                title="阻塞队列 (Blocking queues)"
                :concept-data="blockingQueuesData"
                @interaction="handleInteraction"
              >
                <div class="blocking-queues-showcase">
                  <h3>🚦 阻塞队列：生产者-消费者的完美桥梁</h3>

                  <!-- 概念定义 -->
                  <div class="concept-definition">
                    <h4>📖 概念定义 (What is it?)</h4>
                    <div class="definition-content">
                      <p>
                        <strong>BlockingQueue</strong> 是一个接口，它在普通 Queue
                        的基础上增加了两个关键的阻塞操作：当队列满时，尝试入队的线程会被阻塞（<code
                          >put</code
                        >
                        方法）；当队列空时，尝试出队的线程会被阻塞（<code>take</code> 方法）。
                      </p>
                      <div class="human-explanation">
                        <h5>🗣️ 人话版解释</h5>
                        <p>
                          一个"智能"的队列。它不仅能存放东西，还能在没东西可取或没空间可放时，自动让线程"暂停等待"，并在条件满足时自动唤醒它们。
                        </p>
                      </div>
                    </div>
                  </div>

                  <!-- 深度解读 -->
                  <div class="deep-analysis">
                    <h4>🔍 深度解读与"第一性原理"追问 (Why is it so?)</h4>
                    <div class="analysis-content">
                      <div class="existence-value">
                        <h5>💡 存在价值</h5>
                        <p>
                          为了优雅地解决<strong>生产者-消费者问题</strong>。在并发系统中，经常有一类线程（生产者）负责创建或接收数据，另一类线程（消费者）负责处理这些数据。阻塞队列作为两者之间的"缓冲区"，起到了三大作用：
                        </p>
                        <div class="value-points">
                          <div class="value-point">
                            <span class="point-icon">🔗</span>
                            <div>
                              <strong>解耦</strong
                              >：生产者和消费者不需要知道对方的存在，只需与队列交互。
                            </div>
                          </div>
                          <div class="value-point">
                            <span class="point-icon">📊</span>
                            <div>
                              <strong>削峰填谷</strong
                              >：当生产者速度快于消费者时，数据可以先在队列中缓存，避免丢失。
                            </div>
                          </div>
                          <div class="value-point">
                            <span class="point-icon">🚦</span>
                            <div>
                              <strong>流量控制/反压(Back Pressure)</strong
                              >：当缓冲区（有界队列）满了，生产者会被自然地阻塞，其生产速度被迫降低，从而与消费者的处理能力相匹配，防止系统因数据堆积而崩溃。
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="core-principle">
                        <h5>⚙️ 核心原理</h5>
                        <p>
                          BlockingQueue 的大多数实现（如 ArrayBlockingQueue）内部都依赖于
                          <code>ReentrantLock</code> 和 <code>Condition</code> 对象。通常会有一个
                          <code>notEmpty</code> 条件用于消费者等待，一个
                          <code>notFull</code> 条件用于生产者等待。
                        </p>
                      </div>
                    </div>
                  </div>

                  <!-- 实例与类比 -->
                  <div class="examples-analogies">
                    <h4>🎯 实例与类比 (How does it work?)</h4>
                    <div class="examples-content">
                      <div class="concrete-case">
                        <h5>💼 具象案例</h5>
                        <p>
                          一个日志系统。业务线程（生产者）产生日志消息，并将消息放入一个
                          BlockingQueue。一个专门的日志写入线程（消费者）不断地从队列中取出消息并写入文件。这样，业务线程就不会因为缓慢的磁盘I/O而被阻塞。
                        </p>
                      </div>
                      <div class="vivid-analogy">
                        <h5>🏭 生动类比</h5>
                        <p>阻塞队列就像一个自动化的物流传送带：</p>
                        <div class="analogy-points">
                          <div class="analogy-point">
                            <code>put()</code
                            >：上游的装配工（生产者）把包裹放到传送带上。如果传送带满了，他的手就会被"卡住"，直到传送带腾出空间。
                          </div>
                          <div class="analogy-point">
                            <code>take()</code
                            >：下游的打包工（消费者）从传送带上取包裹。如果传送带是空的，他就会在原地"打盹"，直到新的包裹被传送过来。
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 横向对比 -->
                  <div class="queues-comparison">
                    <h4>⚖️ 横向对比与关联 (How does it relate?)</h4>
                    <div class="comparison-intro">
                      <p>
                        <strong>关联性</strong
                        >：线程池（ExecutorService）的核心组件之一就是阻塞队列，用于存放待执行的任务。
                      </p>
                    </div>
                    <div class="comparison-table">
                      <div class="table-header">
                        <div class="header-cell">队列类型</div>
                        <div class="header-cell">底层结构</div>
                        <div class="header-cell">容量</div>
                        <div class="header-cell">锁策略</div>
                        <div class="header-cell">适用场景</div>
                      </div>

                      <div class="table-row">
                        <div class="cell queue-name">ArrayBlockingQueue</div>
                        <div class="cell">数组</div>
                        <div class="cell">有界</div>
                        <div class="cell">单锁 + 双Condition</div>
                        <div class="cell">固定容量，性能稳定</div>
                      </div>

                      <div class="table-row">
                        <div class="cell queue-name">LinkedBlockingQueue</div>
                        <div class="cell">链表</div>
                        <div class="cell">可有界/无界</div>
                        <div class="cell">双锁分离</div>
                        <div class="cell">高并发，动态容量</div>
                      </div>

                      <div class="table-row">
                        <div class="cell queue-name">PriorityBlockingQueue</div>
                        <div class="cell">堆</div>
                        <div class="cell">无界</div>
                        <div class="cell">单锁</div>
                        <div class="cell">优先级排序</div>
                      </div>

                      <div class="table-row">
                        <div class="cell queue-name">DelayQueue</div>
                        <div class="cell">堆</div>
                        <div class="cell">无界</div>
                        <div class="cell">单锁</div>
                        <div class="cell">延迟期满才能提取</div>
                      </div>
                    </div>

                    <div class="detailed-comparison">
                      <div class="comparison-detail">
                        <h5>🔍 详细对比分析</h5>
                        <div class="detail-item">
                          <strong>ArrayBlockingQueue</strong
                          >：有界队列，基于数组实现。内部使用一把锁和两个 Condition。性能稳定。
                        </div>
                        <div class="detail-item">
                          <strong>LinkedBlockingQueue</strong>：可有界可无界（默认无界，容量为
                          Integer.MAX_VALUE）。基于链表实现。内部巧妙地使用了两把锁（putLock,
                          takeLock）来分别控制入队和出队操作，使得生产者和消费者可以真正地并行执行，在并发度高时吞吐量通常优于
                          ArrayBlockingQueue。
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 常见误区 -->
                  <div class="common-pitfalls">
                    <h4>⚠️ 常见理论误区与注意事项 (Conceptual Pitfalls)</h4>
                    <div class="pitfalls-content">
                      <div class="pitfall-item fatal">
                        <div class="pitfall-header">
                          <span class="pitfall-icon">💀</span>
                          <h5>天坑：滥用无界队列 LinkedBlockingQueue</h5>
                        </div>
                        <div class="pitfall-description">
                          <p>
                            这是导致生产环境内存溢出（OutOfMemoryError）的最常见原因之一。如果生产者的生产速度持续地、系统性地快于消费者，无界队列会无限增长，直到耗尽所有内存。
                          </p>
                        </div>
                      </div>
                      <div class="pitfall-item warning">
                        <div class="pitfall-header">
                          <span class="pitfall-icon">⚠️</span>
                          <h5>注意：put() 和 take() 是可中断的</h5>
                        </div>
                        <div class="pitfall-description">
                          <p>
                            如果线程在阻塞等待时被中断，会抛出 <code>InterruptedException</code>。
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 项目实践踩坑 -->
                  <div class="real-world-problems">
                    <h4>🏭 项目实践踩坑与解决方案 (Real-World Problems & Solutions)</h4>
                    <div class="problem-case">
                      <div class="problem-description">
                        <h5>🚨 常见问题描述</h5>
                        <p>
                          一个在线交易系统，用户下单请求由 Web
                          线程（生产者）处理，处理逻辑包括一系列校验和数据库操作，最后将订单任务放入一个无界的
                          LinkedBlockingQueue，由后台任务线程池（消费者）处理。上线后，系统在平日运行平稳，但在"双十一"大促期间，随着用户请求量剧增，服务器频繁发生
                          OutOfMemoryError 导致宕机。
                        </p>
                      </div>

                      <div class="root-cause">
                        <h5>🔍 问题根源分析</h5>
                        <p>
                          大促期间，下单请求的生产速度远远超过了后台数据库的处理能力（消费者瓶颈）。由于使用了无界的
                          LinkedBlockingQueue，队列中堆积了数百万个待处理的订单对象，最终耗尽了 JVM
                          的全部堆内存。系统缺乏有效的反压机制。
                        </p>
                      </div>

                      <div class="industry-solutions">
                        <h5>🏆 业界主流解决方案与权衡</h5>
                        <div class="solution-item">
                          <div class="solution-header">
                            <span class="solution-icon">✅</span>
                            <h6>方案：使用有界队列并制定明确的拒绝策略</h6>
                          </div>
                          <div class="solution-content">
                            <p>
                              将 LinkedBlockingQueue 替换为有界的 ArrayBlockingQueue 或创建有界的
                              LinkedBlockingQueue。
                            </p>
                            <div class="code-example">
                              <code
                                >BlockingQueue&lt;Order&gt; queue = new
                                ArrayBlockingQueue&lt;&gt;(10000); // 例如，容量设为1万</code
                              >
                            </div>
                            <div class="solution-details">
                              <p>
                                当队列满时，<code>put()</code> 方法会阻塞生产者线程（即 Web
                                线程），这会使 Tomcat 等 Web
                                容器的工作线程被占满，导致新的用户请求被拒绝或超时。虽然看起来不友好，但这是一种有效的自我保护，防止了整个系统因内存耗尽而崩溃。
                              </p>
                              <p>
                                更好的做法是使用
                                <code>offer(E e, long timeout, TimeUnit unit)</code>
                                方法。当队列满时，它可以尝试等待一段时间，如果超时后仍然无法入队，就返回
                                false。此时，业务代码可以执行明确的拒绝策略。
                              </p>
                            </div>
                            <div class="pros-cons">
                              <div class="pros">
                                <strong>优点</strong
                                >：为系统建立了容量天花板和反压机制，保证了系统的稳定性。
                              </div>
                              <div class="cons">
                                <strong>缺点</strong>：需要额外处理任务被拒绝的情况。
                              </div>
                            </div>
                            <div class="rejection-strategy">
                              <strong>拒绝策略示例</strong
                              >：可以向用户返回一个"系统繁忙，请稍后再试"的友好提示；或者将失败的订单任务持久化到磁盘或专门的降级队列中，以便后续补偿处理。
                            </div>
                          </div>
                        </div>
                        <div class="industry-choice">
                          <h6>🏆 业界选择</h6>
                          <p>
                            在任何生产者-消费者模型中，<strong>使用有界队列是基本准则</strong>。队列的容量需要根据业务压测和内存预算来合理设定。必须设计并实现当队列满时的拒绝策略，这是保证系统在高负载下依然可用的关键。
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 7: Future与异步编程 -->
            <section id="topic-6" class="topic-section" ref="topic6">
              <ExpandableSection
                title="Future与异步编程 (Future & CompletableFuture)"
                :concept-data="futureData"
                @interaction="handleInteraction"
              >
                <div class="future-showcase">
                  <h3>🎫 Future与异步编程：从提货单到智能助手</h3>

                  <div class="future-evolution">
                    <h4>🚀 从Future到CompletableFuture的进化</h4>
                    <div class="evolution-comparison">
                      <div class="comparison-side traditional">
                        <div class="side-header">
                          <span class="side-icon">🎫</span>
                          <h5>Future (传统异步)</h5>
                        </div>
                        <div class="side-content">
                          <div class="concept-explanation">
                            <h6>核心概念</h6>
                            <p>
                              <code>Future</code>
                              是一个接口，代表了一个<strong>异步计算的结果</strong>。它像一个"提货单"，你可以先拿到这个凭证，然后在未来的某个时刻凭它来获取真正的计算结果。
                            </p>
                            <div class="analogy-simple">
                              <strong>类比</strong
                              >：我让厨房去做一份蛋炒饭（一个耗时操作），厨房先给了我一个取餐牌（<code>Future</code>）。我拿着这个牌子不能吃，只能用它来问"饭好了没？"
                              (<code>isDone()</code>) 或者直接在出餐口死等 (<code>get()</code>)。
                            </div>
                          </div>
                          <div class="limitations">
                            <h6>局限性</h6>
                            <ul>
                              <li><code>get()</code> 方法是阻塞的，异步的好处荡然无存</li>
                              <li>无法方便地将多个异步任务串联起来</li>
                              <li>容易导致"回调地狱"</li>
                              <li>异常处理复杂</li>
                            </ul>
                          </div>
                        </div>
                      </div>

                      <div class="evolution-arrow">→</div>

                      <div class="comparison-side modern">
                        <div class="side-header">
                          <span class="side-icon">🤖</span>
                          <h5>CompletableFuture (现代异步)</h5>
                        </div>
                        <div class="side-content">
                          <div class="concept-explanation">
                            <h6>核心概念</h6>
                            <p>
                              <code>CompletableFuture</code> 是
                              <code>Future</code> 的一个极其强大的实现，自 Java 8
                              引入。它支持以非阻塞的方式对计算结果进行链式处理、组合和异常管理。
                            </p>
                            <div class="analogy-simple">
                              <strong>类比</strong
                              >：一个"智能取餐牌"。我可以告诉它："等蛋炒饭好了（<code>thenApply</code>），帮我加点葱花；然后（<code>thenAccept</code>）送到3号桌；如果厨房说没鸡蛋了（<code>exceptionally</code>），就给我换成一份炒面"。整个过程我都可以去做别的事，完全不用自己去等。
                            </div>
                          </div>
                          <div class="advantages">
                            <h6>核心优势</h6>
                            <ul>
                              <li><strong>声明式编程</strong>：链式调用，代码可读性强</li>
                              <li><strong>可组合性</strong>：支持复杂的异步任务编排</li>
                              <li><strong>异常处理</strong>：优雅的异常处理机制</li>
                              <li><strong>完成事件驱动</strong>：基于回调，真正的非阻塞</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="completable-future-deep-dive">
                    <h4>🔍 CompletableFuture深度解析</h4>
                    <div class="core-principle">
                      <h5>⚙️ 核心原理</h5>
                      <p>
                        <strong>存在价值</strong>：解决
                        <code>Future</code>
                        的功能局限性，提供一种<strong>声明式、可组合</strong>的异步编程模型。
                      </p>
                      <p>
                        <strong>核心原理</strong
                        >：<strong>完成事件驱动</strong>和<strong>函数式编程思想</strong>。它内部维护了计算的状态（未完成、正常完成、异常完成）和一个依赖于它的任务链（类似一个回调函数的链表）。
                      </p>
                    </div>

                    <div class="method-comparison">
                      <h5>🔧 关键方法对比</h5>
                      <div class="methods-grid">
                        <div class="method-card sync">
                          <div class="method-header">
                            <span class="method-icon">🔄</span>
                            <h6>同步方法</h6>
                          </div>
                          <div class="method-content">
                            <div class="method-item">
                              <code>thenApply()</code>
                              <span>在当前任务完成的线程上执行</span>
                            </div>
                            <div class="method-item">
                              <code>thenAccept()</code>
                              <span>消费结果，无返回值</span>
                            </div>
                            <div class="method-item">
                              <code>thenRun()</code>
                              <span>执行Runnable，不关心结果</span>
                            </div>
                          </div>
                        </div>

                        <div class="method-card async">
                          <div class="method-header">
                            <span class="method-icon">⚡</span>
                            <h6>异步方法</h6>
                          </div>
                          <div class="method-content">
                            <div class="method-item">
                              <code>thenApplyAsync()</code>
                              <span>提交到线程池中异步执行</span>
                            </div>
                            <div class="method-item">
                              <code>thenAcceptAsync()</code>
                              <span>异步消费结果</span>
                            </div>
                            <div class="method-item">
                              <code>thenRunAsync()</code>
                              <span>异步执行Runnable</span>
                            </div>
                          </div>
                        </div>

                        <div class="method-card combine">
                          <div class="method-header">
                            <span class="method-icon">🔗</span>
                            <h6>组合方法</h6>
                          </div>
                          <div class="method-content">
                            <div class="method-item">
                              <code>thenCombine()</code>
                              <span>组合两个Future的结果</span>
                            </div>
                            <div class="method-item">
                              <code>allOf()</code>
                              <span>等待所有Future完成</span>
                            </div>
                            <div class="method-item">
                              <code>anyOf()</code>
                              <span>等待任意一个Future完成</span>
                            </div>
                          </div>
                        </div>

                        <div class="method-card exception">
                          <div class="method-header">
                            <span class="method-icon">🚨</span>
                            <h6>异常处理</h6>
                          </div>
                          <div class="method-content">
                            <div class="method-item">
                              <code>exceptionally()</code>
                              <span>处理异常情况</span>
                            </div>
                            <div class="method-item">
                              <code>handle()</code>
                              <span>同时处理正常和异常结果</span>
                            </div>
                            <div class="method-item">
                              <code>whenComplete()</code>
                              <span>无论成功失败都会执行</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- 常见误区 -->
                    <div class="future-pitfalls">
                      <h5>⚠️ 常见理论误区与注意事项</h5>
                      <div class="pitfall-item warning">
                        <div class="pitfall-header">
                          <span class="pitfall-icon">⚠️</span>
                          <h6>误区：以为 thenApply 也是异步的</h6>
                        </div>
                        <div class="pitfall-description">
                          <p>
                            它只是注册了一个同步回调，执行该回调的线程是不确定的（可能是上一步任务的线程，也可能是调用
                            thenApply 的线程），但它会阻塞调用链的后续步骤。只有带
                            <strong>Async 后缀</strong>的方法才是真正地将任务派发出去。
                          </p>
                        </div>
                      </div>
                      <div class="pitfall-item warning">
                        <div class="pitfall-header">
                          <span class="pitfall-icon">⚠️</span>
                          <h6>注意：默认线程池的限制</h6>
                        </div>
                        <div class="pitfall-description">
                          <p>
                            CompletableFuture 默认使用的公共 ForkJoinPool
                            是有限的。如果所有异步任务都是长时间的阻塞I/O操作，公共线程池的线程很快会被耗尽，导致后续任务无法执行。
                          </p>
                        </div>
                      </div>
                    </div>

                    <!-- 实际案例 -->
                    <div class="practical-example">
                      <h5>💼 具象案例：电商商品详情页</h5>
                      <p>
                        一个电商应用的商品详情页，需要同时从三个不同的微服务异步获取：1.
                        商品基本信息；2. 用户评价；3. 实时价格。
                      </p>
                      <div class="code-example">
                        <pre><code>CompletableFuture&lt;Product&gt; pFuture = CompletableFuture.supplyAsync(() -&gt; productService.getProduct(id));
CompletableFuture&lt;List&lt;Comment&gt;&gt; cFuture = CompletableFuture.supplyAsync(() -&gt; commentService.getComments(id));
CompletableFuture&lt;Price&gt; priceFuture = CompletableFuture.supplyAsync(() -&gt; priceService.getPrice(id));

CompletableFuture.allOf(pFuture, cFuture, priceFuture).join(); // 等待所有任务完成

Product p = pFuture.get();
List&lt;Comment&gt; c = cFuture.get();
Price price = priceFuture.get();</code></pre>
                      </div>
                    </div>

                    <!-- 项目实践踩坑 -->
                    <div class="real-world-problems">
                      <h5>🏭 项目实践踩坑与解决方案 (Real-World Problems & Solutions)</h5>
                      <div class="problem-case">
                        <div class="problem-description">
                          <h6>🚨 常见问题描述</h6>
                          <p>
                            一个聚合服务需要调用下游A、B、C三个服务。传统实现是串行调用，总耗时 T =
                            T(A) + T(B) + T(C)。为了优化，改用 CompletableFuture
                            并行调用，但发现当某个下游服务（如C）响应极慢或超时时，整个聚合服务的线程池（如Tomcat的工作线程）被卡住，无法响应新的请求，最终导致服务雪崩。
                          </p>
                        </div>

                        <div class="root-cause">
                          <h6>🔍 问题根源分析</h6>
                          <p>
                            虽然发起了异步调用，但在最后汇总结果时，不恰当地在主业务线程（如Tomcat线程）中调用了
                            <code>future.get()</code> 进行无限期阻塞等待。当C服务超时，<code
                              >get()</code
                            >
                            方法就永远等下去，宝贵的业务线程资源被白白占用。
                          </p>
                        </div>

                        <div class="industry-solutions">
                          <h6>🏆 业界主流解决方案与权衡</h6>
                          <div class="solution-item">
                            <div class="solution-header">
                              <span class="solution-icon">✅</span>
                              <strong>方案 A: 为 get() 设置合理的超时时间</strong>
                            </div>
                            <div class="solution-content">
                              <p>永远不要不带超时地调用 <code>get()</code>。</p>
                              <div class="code-example">
                                <code>future.get(3, TimeUnit.SECONDS);</code>
                              </div>
                              <div class="pros-cons">
                                <div class="pros">
                                  <strong>优点</strong
                                  >：为阻塞操作设置了"熔断"时间，能有效防止线程被无限期占用，保证了服务的可用性。
                                </div>
                                <div class="cons">
                                  <strong>缺点</strong>：需要在 catch (TimeoutException e)
                                  中编写降级逻辑，例如返回一个默认值或错误信息。
                                </div>
                              </div>
                            </div>
                          </div>
                          <div class="solution-item">
                            <div class="solution-header">
                              <span class="solution-icon">🚀</span>
                              <strong>方案 B: 使用全异步、非阻塞的编程模型</strong>
                            </div>
                            <div class="solution-content">
                              <p>
                                这是更彻底的解决方案。整个处理流程都由 CompletableFuture
                                的回调链驱动，完全不出现
                                <code>get()</code>。最终的结果通过回调函数直接写入
                                HttpServletResponse。
                              </p>
                              <div class="pros-cons">
                                <div class="pros">
                                  <strong>优点</strong
                                  >：从根本上避免了对业务线程的任何阻塞，最大化了吞吐量。这是响应式编程（Reactive
                                  Programming）的核心思想。
                                </div>
                                <div class="cons">
                                  <strong>缺点</strong
                                  >：代码的思维模型与传统的同步顺序模型完全不同，对开发人员的要求更高，调试也更复杂。
                                </div>
                              </div>
                            </div>
                          </div>
                          <div class="industry-choice">
                            <h6>🏆 业界选择</h6>
                            <p>
                              方案A是务实且必要的改进，是所有使用 Future
                              的基本要求。方案B是更先进的架构演进方向，对于追求极致性能和弹性的高吞吐量网关、聚合服务等，正在成为主流。像
                              Spring WebFlux 这样的框架就是对方案B的工程化封装。
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- Topic 8: Executor框架 -->
            <section id="topic-7" class="topic-section" ref="topic7">
              <ExpandableSection
                title="Executor框架 (Executor Framework)"
                :concept-data="executorData"
                @interaction="handleInteraction"
              >
                <div class="executor-showcase">
                  <h3>🏭 Executor框架：任务与执行的完美解耦</h3>

                  <!-- 概念定义 -->
                  <div class="concept-definition">
                    <h4>📖 概念定义 (What is it?)</h4>
                    <div class="definition-content">
                      <p>
                        <strong>Executor 框架</strong>是 JUC 提供的用于管理和执行异步任务（Runnable
                        或 Callable）的一整套标准机制。它的核心思想是将任务的提交与任务的执行解耦。
                      </p>
                      <div class="human-explanation">
                        <h5>🗣️ 人话版解释</h5>
                        <p>
                          你只管把"要做的事情"（任务）扔给它，它会负责找工人（线程）去干活，你完全不用关心工人是怎么找的、有多少个、干完活是辞退还是待命。
                        </p>
                      </div>
                    </div>
                  </div>

                  <!-- 深度解读 -->
                  <div class="deep-analysis">
                    <h4>🔍 深度解读与"第一性原理"追问 (Why is it so?)</h4>
                    <div class="analysis-content">
                      <div class="existence-value">
                        <h5>💡 存在价值</h5>
                        <p>手动创建线程 (<code>new Thread().start()</code>) 有两大弊端：</p>
                        <div class="value-points">
                          <div class="value-point">
                            <span class="point-icon">💰</span>
                            <div>
                              <strong>开销大</strong
                              >：线程是操作系统的宝贵资源，其创建和销毁涉及昂贵的系统调用。
                            </div>
                          </div>
                          <div class="value-point">
                            <span class="point-icon">🎛️</span>
                            <div>
                              <strong>难管理</strong>：无限制地创建线程会迅速耗尽系统资源，导致
                              OutOfMemoryError。你也无法方便地控制并发线程的数量。
                            </div>
                          </div>
                        </div>
                      </div>
                      <div class="core-principle">
                        <h5>⚙️ 核心原理</h5>
                        <p>
                          <strong>线程池 (Thread Pool)</strong
                          >。预先创建一定数量的线程并让它们保持存活状态。当有新任务到来时，从池中取一个空闲线程来执行。任务执行完毕后，线程并不销毁，而是返回池中等待下一个任务。这极大地降低了线程创建和销毁的开销，并使得对并发度的控制成为可能。
                        </p>
                      </div>
                    </div>
                  </div>

                  <!-- 实例与类比 -->
                  <div class="examples-analogies">
                    <h4>🎯 实例与类比 (How does it work?)</h4>
                    <div class="examples-content">
                      <div class="concrete-cases">
                        <h5>💼 具象案例</h5>
                        <div class="case-grid">
                          <div class="case-item">
                            <strong>Executors.newFixedThreadPool(10)</strong
                            >：创建一个固定大小为10的线程池。永远就这10个线程。如果任务多于10个，多余的就在队列里等着。
                          </div>
                          <div class="case-item">
                            <strong>Executors.newCachedThreadPool()</strong
                            >：创建一个可缓存的线程池。来一个任务，如果没有空闲线程，就创建一个新线程。线程空闲60秒后会被回收。
                          </div>
                          <div class="case-item">
                            <strong>Executors.newSingleThreadExecutor()</strong
                            >：只有一个线程的线程池，保证所有任务按提交顺序串行执行。
                          </div>
                          <div class="case-item">
                            <strong>Executors.newScheduledThreadPool(5)</strong
                            >：一个支持定时及周期性任务执行的线程池。
                          </div>
                        </div>
                      </div>
                      <div class="vivid-analogy">
                        <h5>🏭 生动类比</h5>
                        <p>Executor 框架就像一个劳务派遣公司：</p>
                        <div class="analogy-points">
                          <div class="analogy-point">
                            <strong>FixedThreadPool</strong
                            >：公司只有10个正式员工，活再多也只有这10个人干，干不完的活先登记下来（放入队列）。
                          </div>
                          <div class="analogy-point">
                            <strong>CachedThreadPool</strong
                            >：公司主要靠签临时工，来一个项目就招一批人，项目结束了人就解散。
                          </div>
                          <div class="analogy-point">
                            <strong>ScheduledThreadPool</strong
                            >：专门负责像"每天早上9点打卡"、"每隔1小时巡逻一次"这样的定时任务。
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 横向对比与关联 -->
                  <div class="horizontal-comparison">
                    <h4>⚖️ 横向对比与关联 (How does it relate?)</h4>
                    <div class="comparison-intro">
                      <p>
                        <strong>关联性</strong>：它是 JUC 的执行层，是 CompletableFuture
                        等高级工具的底层依赖。其内部通常使用 BlockingQueue 来存储待处理的任务。
                      </p>
                    </div>
                  </div>

                  <!-- 常见误区 -->
                  <div class="common-pitfalls">
                    <h4>⚠️ 常见理论误区与注意事项 (Conceptual Pitfalls)</h4>
                    <div class="pitfalls-content">
                      <div class="pitfall-item fatal">
                        <div class="pitfall-header">
                          <span class="pitfall-icon">💀</span>
                          <h5>天坑：阿里巴巴开发手册禁用 Executors</h5>
                        </div>
                        <div class="pitfall-description">
                          <p>
                            在《阿里巴巴Java开发手册》中，明确禁止使用
                            <code>Executors</code> 类来创建线程池，而是推荐通过
                            <code>ThreadPoolExecutor</code> 的构造函数手动创建。
                          </p>
                          <div class="pitfall-reasons">
                            <h6>原因：</h6>
                            <ul>
                              <li>
                                <code>newFixedThreadPool</code> 和
                                <code>newSingleThreadExecutor</code> 使用的
                                <code>LinkedBlockingQueue</code>
                                默认是无界的，任务堆积可能导致内存溢出。
                              </li>
                              <li>
                                <code>newCachedThreadPool</code> 允许创建的线程数量为
                                <code>Integer.MAX_VALUE</code>，可能会创建大量线程，耗尽系统资源。
                              </li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 项目实践踩坑 -->
                  <div class="real-world-problems">
                    <h4>🏭 项目实践踩坑与解决方案 (Real-World Problems & Solutions)</h4>
                    <div class="problem-case">
                      <div class="problem-description">
                        <h5>🚨 常见问题描述</h5>
                        <p>
                          一个服务使用了
                          <code>Executors.newFixedThreadPool(10)</code>
                          来处理业务。在某个业务高峰期，大量请求涌入，导致任务在线程池的队列中大量堆积。虽然服务没有崩溃，但后续请求的响应时间变得极长，几乎不可用。当服务重启时，发现大量未处理的任务丢失了。
                        </p>
                      </div>

                      <div class="root-cause">
                        <h5>🔍 问题根源分析</h5>
                        <div class="cause-points">
                          <div class="cause-point">
                            使用了无界队列，导致任务无限堆积，无法对上游产生反压。
                          </div>
                          <div class="cause-point">缺乏对线程池状态的监控。</div>
                          <div class="cause-point">
                            没有优雅地关闭线程池，导致JVM退出时，队列中的任务被丢弃。
                          </div>
                        </div>
                      </div>

                      <div class="industry-solutions">
                        <h5>🏆 业界主流解决方案与权衡</h5>
                        <div class="solution-item">
                          <div class="solution-header">
                            <span class="solution-icon">✅</span>
                            <h6>方案：使用 ThreadPoolExecutor 的构造函数进行精细化定制</h6>
                          </div>
                          <div class="solution-content">
                            <div class="code-example">
                              <pre><code>int corePoolSize = 10;
int maxPoolSize = 20;
long keepAliveTime = 60;
TimeUnit unit = TimeUnit.SECONDS;
BlockingQueue&lt;Runnable&gt; workQueue = new ArrayBlockingQueue&lt;&gt;(100); // 使用有界队列！
ThreadFactory threadFactory = new ThreadFactoryBuilder().setNameFormat("my-pool-%d").build();
RejectedExecutionHandler rejectionHandler = new ThreadPoolExecutor.CallerRunsPolicy(); // 定义拒绝策略

ExecutorService pool = new ThreadPoolExecutor(
    corePoolSize, maxPoolSize, keepAliveTime, unit,
    workQueue, threadFactory, rejectionHandler);</code></pre>
                            </div>
                            <div class="solution-benefits">
                              <h6>优点：</h6>
                              <div class="benefit-item">
                                <strong>资源可控</strong>：通过 corePoolSize, maxPoolSize 和有界的
                                workQueue，可以精确控制资源使用，防止资源耗尽。
                              </div>
                              <div class="benefit-item">
                                <strong>明确的拒绝策略</strong
                                >：当线程和队列都满时，可以明确指定如何处理新任务（如：AbortPolicy-抛异常，CallerRunsPolicy-提交任务的线程自己执行，DiscardPolicy-直接丢弃，DiscardOldestPolicy-丢弃队列头部的任务）。
                              </div>
                              <div class="benefit-item">
                                <strong>可监控</strong>：ThreadPoolExecutor 提供了
                                getQueue().size(), getActiveCount()
                                等方法，可以方便地监控线程池的运行状态，并接入监控系统进行报警。
                              </div>
                              <div class="benefit-item">
                                <strong>优雅关闭</strong>：通过 shutdown() (等待已提交任务完成) 和
                                shutdownNow() (尝试中断所有任务)
                                方法，可以安全地关闭线程池，确保资源被正确释放。
                              </div>
                            </div>
                            <div class="solution-tradeoff">
                              <strong>权衡</strong>：配置比 Executors
                              工厂方法复杂，需要开发者对线程池的各个参数有深入的理解。但这正是构建一个健壮的生产级系统所必需的。
                            </div>
                          </div>
                        </div>
                        <div class="industry-choice">
                          <h6>🏆 业界选择</h6>
                          <p>
                            在生产环境中，<strong
                              >永远使用 ThreadPoolExecutor 构造函数来创建线程池</strong
                            >。这是一个强制性的最佳实践。
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </ExpandableSection>
            </section>

            <!-- 章节总结 -->
            <section class="chapter-summary">
              <div class="summary-content">
                <h2>🎯 第六章核心要点总结</h2>
                <div class="key-takeaways">
                  <div class="takeaway-item">
                    <div class="takeaway-icon">🏗️</div>
                    <div>
                      <h4>JUC包的革命性意义</h4>
                      <p>
                        从synchronized的"手动挡"升级到JUC的"自动挡"，提供了更高性能、更灵活的并发编程工具集
                      </p>
                    </div>
                  </div>
                  <div class="takeaway-item">
                    <div class="takeaway-icon">⚛️</div>
                    <div>
                      <h4>原子类的无锁优势</h4>
                      <p>
                        基于CAS的无锁算法在低到中等竞争下性能卓越，LongAdder是高并发计数的最佳实践
                      </p>
                    </div>
                  </div>
                  <div class="takeaway-item">
                    <div class="takeaway-icon">🔐</div>
                    <div>
                      <h4>显式锁的强大功能</h4>
                      <p>
                        ReentrantLock提供了公平性、可中断、超时等高级特性，Condition实现了精确的线程协作
                      </p>
                    </div>
                  </div>
                  <div class="takeaway-item">
                    <div class="takeaway-icon">📦</div>
                    <div>
                      <h4>并发容器的分化设计</h4>
                      <p>ConcurrentHashMap针对高并发读写，CopyOnWriteArrayList优化读多写少场景</p>
                    </div>
                  </div>
                  <div class="takeaway-item">
                    <div class="takeaway-icon">🎯</div>
                    <div>
                      <h4>同步辅助工具的协作艺术</h4>
                      <p>CountDownLatch、CyclicBarrier、Semaphore各有专长，简化复杂线程协作场景</p>
                    </div>
                  </div>
                  <div class="takeaway-item">
                    <div class="takeaway-icon">🚦</div>
                    <div>
                      <h4>阻塞队列的反压机制</h4>
                      <p>
                        BlockingQueue是生产者-消费者模式的完美桥梁，有界队列提供自然的流量控制和反压
                      </p>
                    </div>
                  </div>
                  <div class="takeaway-item">
                    <div class="takeaway-icon">🎫</div>
                    <div>
                      <h4>异步编程的进化</h4>
                      <p>
                        从Future的"提货单"到CompletableFuture的"智能助手"，声明式异步编程成为主流
                      </p>
                    </div>
                  </div>
                  <div class="takeaway-item">
                    <div class="takeaway-icon">🏭</div>
                    <div>
                      <h4>Executor框架的生产实践</h4>
                      <p>
                        禁用Executors工厂方法，手动配置ThreadPoolExecutor是生产环境的强制性最佳实践
                      </p>
                    </div>
                  </div>
                  <div class="takeaway-item">
                    <div class="takeaway-icon">🔧</div>
                    <div>
                      <h4>项目实践踩坑与解决方案</h4>
                      <p>从高并发计数器性能瓶颈到线程池配置策略，业界最佳实践助力避坑</p>
                    </div>
                  </div>
                </div>

                <!-- 思维导图部分 -->
                <div class="mind-map-section">
                  <div class="mindmap-container">
                    <h3>🧠 第六章知识脉络图</h3>
                    <div class="mindmap-wrapper">
                      <div id="chapter6-mindmap" class="mermaid-container">
                        <div class="mindmap-placeholder">
                          <p>🎨 正在生成思维导图...</p>
                          <p>如果长时间未显示，请刷新页面</p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </section>
          </main>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import ExpandableSection from '@/components/ExpandableSection.vue'

// 响应式数据
const progress = ref(0)
const currentTopic = ref(0)
const showNotes = ref(false)
const showQuizModal = ref(false)

// 课程主题
const courseTopics = [
  {
    title: '现代并发构建基石',
    description: 'JUC包的设计理念与架构全览',
  },
  {
    title: '原子类详解',
    description: 'CAS机制与无锁编程实践',
  },
  {
    title: '显式锁与条件',
    description: 'Lock接口与Condition的强大功能',
  },
  {
    title: '同步辅助工具',
    description: 'CountDownLatch等协作工具',
  },
  {
    title: '并发容器',
    description: 'ConcurrentHashMap与CopyOnWriteArrayList',
  },
  {
    title: '阻塞队列',
    description: '生产者-消费者模式的经典实现',
  },
  {
    title: 'Future与异步编程',
    description: 'CompletableFuture的声明式编程',
  },
  {
    title: 'Executor框架',
    description: '任务执行与线程池管理',
  },
]

// 概念数据
const modernConcurrencyData = {
  title: '现代并发应用构建基石',
  keyPoints: [
    'JUC包是Java并发编程的核心工具集，提供高级并发组件',
    '设计理念：分离关注点与显式化，替代语言关键字',
    '基于CAS无锁算法和AQS框架的统一抽象',
    '从简单原子操作到复杂异步编程的完整工具链',
    '解决synchronized和wait/notify的性能和功能局限',
  ],
}

const atomicClassesData = {
  title: '原子类',
  keyPoints: [
    '原子类基于CAS实现无锁并发，避免synchronized的性能开销',
    'CAS三要素：内存位置V，预期值A，新值B',
    'AtomicInteger/Long提供基本原子操作，API丰富',
    'LongAdder通过分段Cell解决高竞争下的性能问题',
    'AtomicReference支持对象引用的原子操作',
  ],
}

const lockConditionData = {
  title: '显式锁与条件',
  keyPoints: [
    'ReentrantLock提供比synchronized更丰富的功能',
    '支持公平锁、可中断锁获取、尝试获取锁等高级特性',
    'Condition提供精确的线程等待和唤醒机制',
    '基于AQS框架实现，使用CLH队列管理等待线程',
    '必须在finally块中释放锁，避免死锁风险',
  ],
}

const synchronizersData = {
  title: '同步辅助工具',
  keyPoints: [
    'CountDownLatch实现一次性的倒计时同步',
    'CyclicBarrier支持循环使用的屏障同步',
    'Semaphore实现资源访问的数量控制',
    '这些工具简化了复杂的线程协作场景',
    '都基于AQS框架，提供一致的使用体验',
  ],
}

const concurrentContainersData = {
  title: '并发容器',
  keyPoints: [
    'ConcurrentHashMap通过分段锁实现高并发访问',
    'Java 8+版本改用CAS+Node锁，性能进一步提升',
    'CopyOnWriteArrayList适用于读多写少场景',
    '写时复制确保读操作完全无锁，迭代安全',
    '选择合适的并发容器是性能优化的关键',
  ],
}

const blockingQueuesData = {
  title: '阻塞队列',
  keyPoints: [
    'BlockingQueue在普通Queue基础上增加阻塞操作：队列满时put()阻塞，队列空时take()阻塞',
    'ArrayBlockingQueue有界基于数组，单锁+双Condition，性能稳定',
    'LinkedBlockingQueue可有界可无界，双锁分离，支持更高并发',
    '提供自然的流量控制和反压机制，是生产者-消费者模式的完美桥梁',
    '避免使用无界队列防止内存溢出，有界队列是生产环境基本准则',
  ],
}

const futureData = {
  title: 'Future与异步编程',
  keyPoints: [
    'Future是异步计算结果的"提货单"，但get()方法阻塞，功能局限',
    'CompletableFuture是"智能提货单"，支持链式调用和函数式编程',
    '基于完成事件驱动和函数式编程思想，提供声明式异步编程模型',
    'thenApply vs thenApplyAsync：同步回调vs异步链，理解执行线程的区别',
    '永远不要不带超时地调用get()，使用全异步编程模型是高性能架构方向',
  ],
}

const executorData = {
  title: 'Executor框架',
  keyPoints: [
    'Executor框架将任务提交与执行解耦，解决手动创建线程的开销大和难管理问题',
    '线程池预先创建线程并复用，极大降低创建销毁开销，控制并发度',
    'ThreadPoolExecutor提供精细化配置：corePoolSize、maxPoolSize、workQueue、rejectionHandler',
    '阿里巴巴开发手册禁用Executors工厂方法，推荐手动构造ThreadPoolExecutor',
    '生产环境必须使用有界队列和明确拒绝策略，防止资源耗尽和任务丢失',
  ],
}

// 方法
const scrollToTopic = (index: number) => {
  currentTopic.value = index

  // 滚动到对应的主题部分
  const targetElement = document.getElementById(`topic-${index}`)
  if (targetElement) {
    targetElement.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
    })
  }
}

const toggleNotes = () => {
  showNotes.value = !showNotes.value
}

const showQuiz = () => {
  showQuizModal.value = true
}

const handleInteraction = (type: string) => {
  console.log('Interaction:', type)
}

// 生命周期
onMounted(async () => {
  // 渲染思维导图
  await renderMindMap()

  // 模拟进度更新
  const interval = setInterval(() => {
    if (progress.value < 100) {
      progress.value += 2
    } else {
      clearInterval(interval)
    }
  }, 100)
})

const renderMindMap = async () => {
  try {
    console.log('开始初始化 Mermaid...')
    const mermaid = await import('mermaid')
    console.log('Mermaid 模块加载成功:', mermaid)

    mermaid.default.initialize({
      startOnLoad: false,
      theme: 'default',
      securityLevel: 'loose',
    })
    console.log('Mermaid 初始化完成')

    // 延迟渲染以确保DOM已加载
    setTimeout(async () => {
      try {
        console.log('开始渲染 Mermaid 图表...')

        // 创建思维导图内容
        const mindmapContent = `mindmap
  root((第六章 JDK并发库))
    现代并发基石
      JUC包意义
        解放synchronized局限
        显式化API设计
        性能与功能并重
      设计原理
        分离关注点
        CAS无锁算法
        AQS统一框架
        函数式编程
      架构层次
        应用层
        JUC工具层
        基础框架层
        硬件指令层
    原子操作
      AtomicInteger/Long
        基本原子操作
        CAS自旋重试
        API丰富易用
      AtomicReference
        对象引用原子性
        ABA问题解决
        函数式更新
      LongAdder
        分段Cell设计
        高竞争性能优化
        空间换时间策略
      核心原理
        CAS三要素
        硬件支持
        乐观锁思想
      项目实践踩坑
        高并发计数器瓶颈
        LongAdder最佳实践
        性能测试驱动选择
    显式锁与协作
      ReentrantLock
        公平非公平选择
        可中断锁获取
        尝试获取超时
      Condition
        精确等待唤醒
        多条件变量
        替代wait notify
      实践要点
        finally释放锁
        避免死锁
        性能vs功能权衡
      项目实践踩坑
        盲目替换synchronized
        功能驱动vs性能驱动
        JMH基准测试验证
    同步辅助工具
      CountDownLatch
        一次性倒计时
        等待N个事件
        启动协调场景
      CyclicBarrier
        循环屏障同步
        阶段性任务
        可重置计数器
      Semaphore
        资源数量控制
        限流器实现
        许可证模式
      项目实践踩坑
        CountDownLatch死锁
        try-finally保证countDown
        带超时的await兜底
    并发容器
      ConcurrentHashMap
        分段锁Java7
        CAS Node锁Java8
        高并发读写优化
      CopyOnWriteArrayList
        写时复制策略
        读多写少优化
        迭代器安全
      选择原则
        场景驱动
        性能测试验证
        读写比例考量
    阻塞队列
      ArrayBlockingQueue
        有界数组实现
        单锁双Condition
        性能稳定
      LinkedBlockingQueue
        可有界链表
        双锁分离
        高并发支持
      应用模式
        生产者消费者
        流量控制
        反压机制
    异步编程
      Future局限
        阻塞get方法
        组合困难
        异常处理不便
      CompletableFuture
        链式调用
        函数式编程
        丰富组合操作
      最佳实践
        避免阻塞等待
        异常处理策略
        超时控制机制
    任务执行框架
      Executor接口
        任务提交执行解耦
        统一抽象
        可扩展设计
      ThreadPoolExecutor
        核心最大线程数
        队列选择策略
        拒绝处理机制
      实践警告
        避免Executors工厂
        手动配置参数
        监控线程池状态`

        const container = document.getElementById('chapter6-mindmap')
        if (container) {
          container.innerHTML = mindmapContent
          await mermaid.default.run({
            nodes: [container],
          })
          console.log('Mermaid 图表渲染成功')
        }
      } catch (error) {
        console.error('渲染 Mermaid 图表时出错:', error)
        const container = document.getElementById('chapter6-mindmap')
        if (container) {
          container.innerHTML = `
            <div class="mindmap-placeholder">
              <p>💡 思维导图渲染遇到问题</p>
              <p>建议刷新页面重新加载</p>
            </div>
          `
        }
      }
    }, 1000)
  } catch (error) {
    console.error('初始化 Mermaid 时出错:', error)
  }
}
</script>

<style scoped>
/* 基础样式 */
.java-chapter6 {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 页面头部样式 */
.chapter-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 4rem 0;
  position: relative;
  overflow: hidden;
}

.chapter-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.1' fill-rule='evenodd'%3E%3Cpath d='M20 20c0 11.046-8.954 20-20 20s-20-8.954-20-20 8.954-20 20-20 20 8.954 20 20z'/%3E%3C/g%3E%3C/svg%3E");
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.header-content {
  text-align: center;
  position: relative;
  z-index: 1;
}

.chapter-title {
  font-size: 3rem;
  margin: 0 0 1rem 0;
  font-weight: 700;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.chapter-subtitle {
  font-size: 1.3rem;
  margin: 0 0 2rem 0;
  opacity: 0.9;
  font-weight: 300;
}

.chapter-badge {
  display: inline-block;
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem 1.5rem;
  border-radius: 25px;
  margin-bottom: 2rem;
  backdrop-filter: blur(10px);
}

.badge-text {
  font-weight: 600;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.progress-bar {
  max-width: 400px;
  margin: 0 auto;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 10px;
  padding: 4px;
  position: relative;
}

.progress-fill {
  height: 8px;
  background: linear-gradient(90deg, #4facfe 0%, #00f2fe 100%);
  border-radius: 6px;
  transition: width 0.5s ease;
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 0.8rem;
  font-weight: 600;
  color: white;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
}

/* 内容布局 */
.content-wrapper {
  padding: 2rem 0;
}

.content-layout {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 2rem;
  align-items: start;
}

/* 侧边栏样式 */
.sidebar {
  position: sticky;
  top: 2rem;
}

.outline {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;
}

.outline h3 {
  margin: 0 0 1.5rem 0;
  color: #333;
  font-size: 1.2rem;
  text-align: center;
}

.outline-grid {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.outline-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.outline-item:hover {
  background: #f8f9fa;
  border-color: #e9ecef;
}

.outline-item.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-color: transparent;
}

.outline-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: 600;
  color: #666;
  flex-shrink: 0;
}

.outline-item.active .outline-number {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

.outline-content h4 {
  margin: 0 0 0.25rem 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.outline-content p {
  margin: 0;
  font-size: 0.8rem;
  opacity: 0.8;
  line-height: 1.3;
}

.toolbar {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.tool-button {
  background: white;
  color: #333;
  border: 2px solid #e9ecef;
  padding: 0.75rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-weight: 500;
  text-align: center;
}

.tool-button:hover {
  background: #f8f9fa;
  border-color: #667eea;
  color: #667eea;
}

/* 主内容区样式 */
.main-content {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.topic-section {
  margin-bottom: 2rem;
}

/* JUC展示样式 */
.modern-concurrency-showcase {
  padding: 2rem;
}

.modern-concurrency-showcase h3 {
  color: #333;
  margin-bottom: 2rem;
  font-size: 2rem;
  text-align: center;
}

.juc-evolution {
  margin: 2rem 0;
}

.juc-evolution h4 {
  color: #333;
  margin-bottom: 1.5rem;
  font-size: 1.4rem;
}

.evolution-timeline {
  display: flex;
  align-items: stretch;
  gap: 2rem;
  margin: 2rem 0;
}

.timeline-item {
  flex: 1;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.timeline-item.past {
  border-top: 4px solid #e74c3c;
}

.timeline-item.present {
  border-top: 4px solid #27ae60;
}

.timeline-header {
  background: #f8f9fa;
  padding: 1.5rem;
  text-align: center;
  border-bottom: 1px solid #e9ecef;
}

.timeline-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.timeline-header h5 {
  margin: 0;
  color: #333;
  font-size: 1.1rem;
}

.timeline-content {
  padding: 1.5rem;
}

.timeline-content p {
  margin: 0 0 1rem 0;
  color: #555;
  font-weight: 500;
}

.evolution-details h6 {
  margin: 1rem 0 0.5rem 0;
  color: #333;
  font-size: 0.95rem;
}

.evolution-details ul {
  margin: 0;
  padding-left: 1.5rem;
  list-style-type: disc;
}

.evolution-details li {
  margin-bottom: 0.3rem;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

.timeline-arrow {
  display: flex;
  align-items: center;
  font-size: 2rem;
  color: #667eea;
  font-weight: bold;
}

/* 原理展示样式 */
.juc-principles {
  margin: 3rem 0;
}

.principles-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.principle-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.principle-card:hover {
  transform: translateY(-4px);
}

.principle-card.performance {
  border-top: 4px solid #f39c12;
}

.principle-card.flexibility {
  border-top: 4px solid #3498db;
}

.principle-card.composability {
  border-top: 4px solid #9b59b6;
}

.principle-header {
  padding: 1.5rem;
  text-align: center;
  background: #f8f9fa;
}

.principle-icon {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
}

.principle-header h5 {
  margin: 0;
  color: #333;
  font-size: 1.2rem;
}

.principle-content {
  padding: 1.5rem;
}

.principle-content p {
  margin: 0 0 1rem 0;
  color: #555;
  line-height: 1.6;
}

.principle-details h6 {
  margin: 1rem 0 0.5rem 0;
  color: #333;
  font-size: 0.95rem;
}

.principle-details ul {
  margin: 0;
  padding-left: 1.5rem;
}

.principle-details li {
  margin-bottom: 0.3rem;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

/* 架构图样式 */
.juc-architecture {
  margin: 3rem 0;
}

.architecture-diagram {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  margin: 2rem 0;
  padding: 2rem;
  background: #f8f9fa;
  border-radius: 12px;
}

.arch-layer {
  width: 100%;
  max-width: 800px;
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.arch-layer.application {
  border-left: 4px solid #e74c3c;
}

.arch-layer.juc-tools {
  border-left: 4px solid #3498db;
}

.arch-layer.foundation {
  border-left: 4px solid #f39c12;
}

.arch-layer.hardware {
  border-left: 4px solid #27ae60;
}

.arch-layer h5 {
  margin: 0 0 1rem 0;
  text-align: center;
  color: #333;
  font-size: 1.1rem;
}

.arch-components {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.arch-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 1rem;
}

.arch-group {
  text-align: center;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 6px;
}

.arch-group h6 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 0.9rem;
  font-weight: 600;
}

.arch-group span {
  display: block;
  margin: 0.2rem 0;
  color: #666;
  font-size: 0.8rem;
}

.component {
  padding: 0.5rem 1rem;
  background: #e9ecef;
  border-radius: 6px;
  font-size: 0.85rem;
  color: #495057;
}

.arch-arrow {
  font-size: 1.5rem;
  color: #667eea;
  font-weight: bold;
}

/* 原子类展示样式 */
.atomic-classes-showcase {
  padding: 2rem;
}

.atomic-motivation {
  margin: 2rem 0;
}

.motivation-comparison {
  display: flex;
  align-items: stretch;
  gap: 2rem;
  margin: 2rem 0;
}

.comparison-item {
  flex: 1;
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.comparison-item.problem {
  border-top: 4px solid #e74c3c;
}

.comparison-item.solution {
  border-top: 4px solid #27ae60;
}

.comparison-header {
  background: #f8f9fa;
  padding: 1.5rem;
  text-align: center;
  border-bottom: 1px solid #e9ecef;
}

.comparison-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.comparison-header h5 {
  margin: 0;
  color: #333;
  font-size: 1.1rem;
}

.comparison-content {
  padding: 1.5rem;
}

.comparison-content h6 {
  margin: 1rem 0 0.5rem 0;
  color: #333;
  font-size: 0.95rem;
}

.comparison-content ul {
  margin: 0 0 1rem 0;
  padding-left: 1.5rem;
}

.comparison-content li {
  margin-bottom: 0.3rem;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

.comparison-arrow {
  display: flex;
  align-items: center;
  font-size: 2rem;
  color: #667eea;
  font-weight: bold;
}

/* CAS原理样式 */
.cas-principle {
  margin: 3rem 0;
}

.cas-explanation {
  background: #f8f9fa;
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
}

.cas-formula {
  margin-bottom: 2rem;
}

.cas-formula h5 {
  margin: 0 0 1rem 0;
  color: #333;
  text-align: center;
}

.formula-display {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.cas-params {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.param-item {
  text-align: center;
}

.param-symbol {
  display: block;
  font-size: 1.5rem;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 0.5rem;
}

.param-desc {
  font-size: 0.9rem;
  color: #666;
}

.cas-logic {
  text-align: center;
  padding: 1rem;
  background: #2d3748;
  border-radius: 6px;
  margin-top: 1rem;
}

.cas-logic code {
  color: #e2e8f0;
  font-family: 'Fira Code', monospace;
  font-size: 0.9rem;
}

/* CAS演示样式 */
.cas-demo {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.demo-scenario {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.scenario-step {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.step-number {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background: #667eea;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-content h6 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1rem;
}

.step-content p {
  margin: 0 0 0.5rem 0;
  color: #555;
  font-size: 0.9rem;
  line-height: 1.4;
}

.memory-visual {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.5rem;
  background: white;
  border-radius: 4px;
  margin-top: 0.5rem;
}

.memory-visual.updated {
  background: #e8f5e8;
}

.memory-address {
  font-family: 'Fira Code', monospace;
  font-size: 0.8rem;
  color: #666;
}

.memory-value {
  font-family: 'Fira Code', monospace;
  font-size: 0.8rem;
  font-weight: bold;
  color: #333;
}

.thread-action {
  padding: 0.5rem;
  border-radius: 4px;
  font-family: 'Fira Code', monospace;
  font-size: 0.8rem;
  margin-top: 0.5rem;
}

.thread-action.thread-a {
  background: #e3f2fd;
  color: #1976d2;
}

.thread-action.thread-b {
  background: #f3e5f5;
  color: #7b1fa2;
}

.thread-action.retry {
  background: #fff3e0;
  color: #f57c00;
}

.thread-action span {
  display: block;
  margin: 0.2rem 0;
}

/* 原子类型样式 */
.atomic-types {
  margin: 3rem 0;
}

.types-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.type-category {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.type-category.basic {
  border-top: 4px solid #3498db;
}

.type-category.reference {
  border-top: 4px solid #9b59b6;
}

.type-category.adder {
  border-top: 4px solid #e67e22;
}

.category-header {
  background: #f8f9fa;
  padding: 1.5rem;
  text-align: center;
  border-bottom: 1px solid #e9ecef;
}

.category-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
}

.category-header h5 {
  margin: 0;
  color: #333;
  font-size: 1.1rem;
}

.type-list {
  padding: 1.5rem;
}

.type-item {
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #f0f0f0;
}

.type-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
}

.type-item.highlight {
  background: #fff8e1;
  margin: -0.5rem;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #ffc107;
}

.type-item h6 {
  margin: 0 0 0.5rem 0;
  color: #333;
  font-size: 1rem;
}

.type-item p {
  margin: 0 0 0.75rem 0;
  color: #666;
  font-size: 0.9rem;
  line-height: 1.4;
}

.api-examples {
  background: #2d3748;
  border-radius: 4px;
  padding: 0.75rem;
  margin: 0.5rem 0;
}

.api-examples code {
  display: block;
  color: #e2e8f0;
  font-family: 'Fira Code', monospace;
  font-size: 0.8rem;
  margin: 0.2rem 0;
}

.performance-note {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.75rem;
  padding: 0.5rem;
  background: #e8f5e8;
  border-radius: 4px;
  border-left: 3px solid #4caf50;
}

.perf-icon {
  font-size: 1rem;
}

.performance-note span:last-child {
  font-size: 0.85rem;
  color: #2e7d32;
  font-weight: 500;
}

/* 章节总结样式 */
.chapter-summary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  overflow: hidden;
  margin-top: 2rem;
}

.summary-content {
  padding: 2rem;
  color: white;
}

.summary-content h2 {
  text-align: center;
  margin: 0 0 2rem 0;
  font-size: 2rem;
}

.key-takeaways {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.takeaway-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1.5rem;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  backdrop-filter: blur(10px);
}

.takeaway-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.takeaway-item h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.1rem;
}

.takeaway-item p {
  margin: 0;
  opacity: 0.9;
  line-height: 1.5;
}

.mind-map-section {
  margin-top: 3rem;
}

.mindmap-container {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.mindmap-container h3 {
  text-align: center;
  color: #333;
  margin-bottom: 2rem;
}

.mindmap-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 600px;
  background: #fafafa;
  border-radius: 8px;
  border: 1px solid #e0e0e0;
}

.mermaid-container {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.mindmap-placeholder {
  text-align: center;
  padding: 3rem;
  color: #666;
}

.mindmap-placeholder p {
  margin: 0.5rem 0;
  font-size: 1.1rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .content-layout {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .sidebar {
    position: static;
    order: 2;
  }

  .chapter-title {
    font-size: 2rem;
  }

  .evolution-timeline {
    flex-direction: column;
  }

  .timeline-arrow {
    transform: rotate(90deg);
    text-align: center;
  }

  .motivation-comparison {
    flex-direction: column;
  }

  .comparison-arrow {
    transform: rotate(90deg);
    text-align: center;
  }

  .cas-params {
    flex-direction: column;
    gap: 1rem;
  }

  .types-grid {
    grid-template-columns: 1fr;
  }

  .key-takeaways {
    grid-template-columns: 1fr;
  }

  .mindmap-wrapper {
    min-height: 400px;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 1rem;
  }

  .principles-grid {
    grid-template-columns: 1fr;
  }

  .demo-scenario {
    gap: 0.5rem;
  }

  .scenario-step {
    flex-direction: column;
    text-align: center;
  }
}

/* Real-World Problems & Solutions 样式 */
.real-world-section {
  margin: 3rem 0;
  padding: 2rem;
  background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
  border-radius: 12px;
  border-left: 4px solid #e53e3e;
}

.real-world-section h4 {
  color: #c53030;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.problems-solutions {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.problem-case {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.case-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid #fed7d7;
}

.case-icon {
  font-size: 1.5rem;
}

.case-header h5 {
  color: #c53030;
  margin: 0;
  font-size: 1.1rem;
}

.case-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.problem-description,
.root-cause {
  padding: 1rem;
  border-radius: 6px;
}

.problem-description {
  background: #fef5e7;
  border-left: 3px solid #f6ad55;
}

.root-cause {
  background: #e6fffa;
  border-left: 3px solid #38b2ac;
}

.problem-description h6,
.root-cause h6 {
  color: #2d3748;
  margin: 0 0 0.5rem 0;
  font-weight: 600;
}

.solutions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.solution-option {
  border-radius: 8px;
  padding: 1.5rem;
  border: 2px solid transparent;
}

.solution-option.recommended {
  background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 100%);
  border-color: #48bb78;
}

.solution-option.alternative {
  background: linear-gradient(135deg, #f7fafc 0%, #e2e8f0 100%);
  border-color: #a0aec0;
}

.solution-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.solution-badge {
  background: #48bb78;
  color: white;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.solution-option.alternative .solution-badge {
  background: #a0aec0;
}

.solution-header h7 {
  color: #2d3748;
  font-size: 1rem;
  font-weight: 600;
  margin: 0;
}

.solution-details {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.pros-cons {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.pros,
.cons {
  padding: 1rem;
  border-radius: 6px;
}

.pros {
  background: #f0fff4;
  border-left: 3px solid #48bb78;
}

.cons {
  background: #fef5e7;
  border-left: 3px solid #ed8936;
}

.pros h8,
.cons h8 {
  color: #2d3748;
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.pros ul,
.cons ul {
  margin: 0;
  padding-left: 1.2rem;
}

.pros li,
.cons li {
  font-size: 0.85rem;
  line-height: 1.4;
  margin-bottom: 0.25rem;
}

.trade-offs {
  background: #edf2f7;
  padding: 1rem;
  border-radius: 6px;
  border-left: 3px solid #4299e1;
}

.trade-offs strong {
  color: #2b6cb0;
}

.code-example {
  background: #1a202c;
  border-radius: 6px;
  padding: 1rem;
  margin: 0.5rem 0;
}

.code-example pre {
  margin: 0;
  color: #e2e8f0;
  font-family: 'Fira Code', monospace;
  font-size: 0.85rem;
  line-height: 1.4;
}

.replacement-scenarios h8 {
  color: #2d3748;
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.replacement-scenarios ul {
  margin: 0;
  padding-left: 1.2rem;
}

.replacement-scenarios li {
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: 0.5rem;
}

/* 同步器卡片样式 */
.synchronizers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin: 2rem 0;
}

.synchronizer-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-top: 4px solid #667eea;
  transition:
    transform 0.2s ease,
    box-shadow 0.2s ease;
}

.synchronizer-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.synchronizer-card.countdown {
  border-top-color: #f6ad55;
}

.synchronizer-card.barrier {
  border-top-color: #4299e1;
}

.synchronizer-card.semaphore {
  border-top-color: #48bb78;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.card-icon {
  font-size: 1.5rem;
}

.card-header h5 {
  margin: 0;
  color: #2d3748;
  font-size: 1.1rem;
}

.card-badge {
  background: #edf2f7;
  color: #4a5568;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.card-content p {
  color: #4a5568;
  margin-bottom: 1rem;
  line-height: 1.5;
}

.use-cases,
.key-methods {
  margin-bottom: 1rem;
}

.use-cases h6,
.key-methods h6 {
  color: #2d3748;
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.use-cases ul {
  margin: 0;
  padding-left: 1.2rem;
}

.use-cases li {
  font-size: 0.85rem;
  line-height: 1.4;
  color: #4a5568;
  margin-bottom: 0.25rem;
}

.key-methods code {
  background: #edf2f7;
  color: #2d3748;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  margin-right: 0.5rem;
}

/* 工作流程样式 */
.workflow-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin: 1rem 0;
}

.workflow-step {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.workflow-step .step-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #667eea;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin: 0 auto 1rem auto;
}

.workflow-step h6 {
  color: #2d3748;
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
}

.workflow-step p {
  color: #4a5568;
  font-size: 0.85rem;
  line-height: 1.4;
  margin: 0;
}

/* 锁进化对比样式 */
.evolution-comparison {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 2rem;
  align-items: center;
  margin: 2rem 0;
}

.comparison-side {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.comparison-side.traditional {
  border-left: 4px solid #e53e3e;
}

.comparison-side.modern {
  border-left: 4px solid #38a169;
}

.side-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid #f7fafc;
}

.side-icon {
  font-size: 1.5rem;
}

.side-header h5 {
  margin: 0;
  color: #2d3748;
  font-size: 1.1rem;
}

.side-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.features,
.limitations,
.responsibilities {
  padding: 1rem;
  border-radius: 6px;
}

.features {
  background: #f0fff4;
  border-left: 3px solid #48bb78;
}

.limitations {
  background: #fef5e7;
  border-left: 3px solid #ed8936;
}

.responsibilities {
  background: #e6fffa;
  border-left: 3px solid #38b2ac;
}

.features h6,
.limitations h6,
.responsibilities h6 {
  color: #2d3748;
  margin: 0 0 0.5rem 0;
  font-weight: 600;
  font-size: 0.9rem;
}

.features ul,
.limitations ul,
.responsibilities ul {
  margin: 0;
  padding-left: 1.2rem;
}

.features li,
.limitations li,
.responsibilities li {
  font-size: 0.85rem;
  line-height: 1.4;
  margin-bottom: 0.25rem;
}

/* Condition优势样式 */
.advantages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin: 1rem 0;
}

.advantage-item {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.advantage-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.advantage-content h6 {
  color: #2d3748;
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.advantage-content p {
  color: #4a5568;
  margin: 0;
  font-size: 0.85rem;
  line-height: 1.4;
}

/* 响应式设计增强 */
@media (max-width: 768px) {
  .synchronizers-grid {
    grid-template-columns: 1fr;
  }

  .pros-cons {
    grid-template-columns: 1fr;
  }

  .workflow-steps {
    grid-template-columns: 1fr;
  }

  .evolution-comparison {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .evolution-arrow {
    transform: rotate(90deg);
    text-align: center;
  }

  .advantages-grid {
    grid-template-columns: 1fr;
  }
}

/* 并发容器样式 */
.containers-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
  margin: 2rem 0;
}

.container-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-top: 4px solid #667eea;
}

.container-card.concurrent-map {
  border-top-color: #4299e1;
}

.container-card.copy-on-write {
  border-top-color: #48bb78;
}

.evolution-timeline {
  margin: 1rem 0;
}

.evolution-timeline .timeline-item {
  background: #f7fafc;
  border-radius: 6px;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  border-left: 3px solid #4299e1;
}

.evolution-timeline .timeline-item h6 {
  color: #2d3748;
  margin: 0 0 0.25rem 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.evolution-timeline .timeline-item p {
  color: #4a5568;
  margin: 0;
  font-size: 0.85rem;
}

.performance-highlights,
.cow-mechanism,
.trade-offs {
  margin: 1rem 0;
}

.performance-highlights h6,
.cow-mechanism h6,
.trade-offs h6 {
  color: #2d3748;
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.performance-highlights ul,
.trade-offs ul {
  margin: 0;
  padding-left: 1.2rem;
}

.performance-highlights li,
.trade-offs li {
  font-size: 0.85rem;
  line-height: 1.4;
  color: #4a5568;
  margin-bottom: 0.25rem;
}

.mechanism-steps {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.step {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  background: #edf2f7;
  border-radius: 6px;
}

.step-num {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #48bb78;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: bold;
  flex-shrink: 0;
}

.step span:last-child {
  font-size: 0.85rem;
  color: #2d3748;
}

.container-selection h8 {
  color: #2d3748;
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.container-selection ul {
  margin: 0;
  padding-left: 1.2rem;
}

.container-selection li {
  font-size: 0.9rem;
  line-height: 1.5;
  margin-bottom: 0.5rem;
}

/* 阻塞队列对比表格样式 */
.comparison-table {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 1rem 0;
}

.table-header {
  display: grid;
  grid-template-columns: 1.5fr 1fr 1fr 1.2fr 1.5fr;
  background: #667eea;
  color: white;
}

.table-row {
  display: grid;
  grid-template-columns: 1.5fr 1fr 1fr 1.2fr 1.5fr;
  border-bottom: 1px solid #e2e8f0;
}

.table-row:last-child {
  border-bottom: none;
}

.table-row:nth-child(even) {
  background: #f7fafc;
}

.header-cell,
.cell {
  padding: 1rem;
  text-align: center;
  font-size: 0.85rem;
}

.header-cell {
  font-weight: 600;
  font-size: 0.9rem;
}

.cell {
  color: #4a5568;
  line-height: 1.4;
}

.queue-name {
  font-weight: 600;
  color: #2d3748;
  text-align: left;
}

/* 响应式设计增强 */
@media (max-width: 768px) {
  .containers-grid {
    grid-template-columns: 1fr;
  }

  .comparison-table {
    font-size: 0.8rem;
  }

  .table-header,
  .table-row {
    grid-template-columns: 1fr;
    text-align: left;
  }

  .header-cell,
  .cell {
    padding: 0.75rem;
    border-bottom: 1px solid #e2e8f0;
  }

  .header-cell {
    background: #667eea;
    color: white;
    font-weight: 600;
  }

  .cell::before {
    content: attr(data-label) ': ';
    font-weight: 600;
    color: #2d3748;
  }
}

/* Lock深度解析样式 */
.lock-definition {
  background: #f7fafc;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border-left: 4px solid #4299e1;
}

.human-explanation {
  background: white;
  border-radius: 6px;
  padding: 1rem;
  margin-top: 1rem;
  border: 1px solid #e2e8f0;
}

.human-explanation h6 {
  color: #2d3748;
  margin: 0 0 0.75rem 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.human-explanation p {
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  line-height: 1.5;
  color: #4a5568;
}

.pain-points-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin: 1rem 0;
}

.pain-point {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.pain-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.pain-content h6 {
  color: #2d3748;
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.pain-content p {
  color: #4a5568;
  margin: 0 0 0.5rem 0;
  font-size: 0.85rem;
  line-height: 1.4;
}

.solution {
  background: #e6fffa;
  color: #2b6cb0;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
  font-family: 'Fira Code', monospace;
}

.aqs-explanation {
  background: #f7fafc;
  border-radius: 8px;
  padding: 1.5rem;
  border-left: 4px solid #38a169;
}

.aqs-workflow {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.workflow-item {
  background: white;
  border-radius: 6px;
  padding: 1rem;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.workflow-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.workflow-content h6 {
  color: #2d3748;
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.workflow-content p {
  color: #4a5568;
  margin: 0;
  font-size: 0.85rem;
  line-height: 1.4;
}

.condition-concept {
  background: #f0fff4;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border-left: 4px solid #48bb78;
}

.producer-consumer-example {
  background: #fef5e7;
  border-radius: 8px;
  padding: 1.5rem;
  margin-top: 1.5rem;
  border-left: 4px solid #f6ad55;
}

.example-description {
  margin-bottom: 1rem;
}

.example-description p {
  color: #4a5568;
  margin: 0;
  font-size: 0.9rem;
  line-height: 1.5;
}

/* 响应式设计增强 */
@media (max-width: 768px) {
  .pain-points-grid {
    grid-template-columns: 1fr;
  }

  .aqs-workflow {
    grid-template-columns: 1fr;
  }

  .workflow-item {
    flex-direction: column;
    text-align: center;
  }
}

/* 类比样式 */
.lock-analogy {
  background: #f0f4f8;
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
  border-left: 4px solid #667eea;
}

.analogy-comparison {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-top: 1rem;
}

.analogy-item {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.analogy-item.traditional {
  border-top: 4px solid #e53e3e;
}

.analogy-item.modern {
  border-top: 4px solid #38a169;
}

.analogy-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid #f7fafc;
}

.analogy-icon {
  font-size: 1.5rem;
}

.analogy-header h6 {
  color: #2d3748;
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
}

.analogy-content ul {
  margin: 0;
  padding-left: 1.2rem;
}

.analogy-content li {
  font-size: 0.9rem;
  line-height: 1.5;
  color: #4a5568;
  margin-bottom: 0.5rem;
}

.lock-methods {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.method-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  background: #f7fafc;
  border-radius: 6px;
}

.method-item code {
  background: #2d3748;
  color: #e2e8f0;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
  min-width: 140px;
  text-align: center;
}

.method-item span {
  font-size: 0.85rem;
  color: #4a5568;
  line-height: 1.4;
}

.condition-analogy {
  background: #e6fffa;
  border-radius: 6px;
  padding: 1rem;
  border-left: 3px solid #38b2ac;
}

.condition-analogy h7 {
  color: #2d3748;
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
  display: block;
}

.condition-analogy p {
  color: #4a5568;
  margin: 0;
  font-size: 0.85rem;
  line-height: 1.5;
}

/* 对比表格样式增强 */
.lock-comparison-table {
  margin: 2rem 0;
}

.feature-name {
  font-weight: 600;
  color: #2d3748;
  text-align: left;
}

.highlight-success {
  background: #f0fff4 !important;
  color: #22543d;
  font-weight: 600;
}

.highlight-danger {
  background: #fed7d7 !important;
  color: #c53030;
  font-weight: 600;
}

/* 常见误区样式 */
.common-pitfalls {
  background: #fff5f5;
  border-radius: 12px;
  padding: 2rem;
  margin: 2rem 0;
  border-left: 4px solid #e53e3e;
}

.pitfalls-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1rem;
}

.pitfall-item {
  background: white;
  border-radius: 8px;
  padding: 1rem;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pitfall-item.critical {
  border-left: 4px solid #e53e3e;
}

.pitfall-item.warning {
  border-left: 4px solid #f6ad55;
}

.pitfall-item.info {
  border-left: 4px solid #4299e1;
}

.pitfall-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.pitfall-content h6 {
  color: #2d3748;
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.pitfall-content p {
  color: #4a5568;
  margin: 0;
  font-size: 0.85rem;
  line-height: 1.5;
}

/* 响应式设计增强 */
@media (max-width: 768px) {
  .analogy-comparison {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .method-item {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }

  .method-item code {
    min-width: auto;
  }

  .pitfall-item {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }
}

/* CountDownLatch深度解析样式 */
.countdown-definition {
  background: #f0fff4;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border-left: 4px solid #48bb78;
}

.countdown-principle {
  background: #f7fafc;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border-left: 4px solid #4299e1;
}

.aqs-implementation {
  background: white;
  border-radius: 6px;
  padding: 1rem;
  margin: 1rem 0;
  border: 1px solid #e2e8f0;
}

.aqs-implementation h6 {
  color: #2d3748;
  margin: 0 0 0.75rem 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.aqs-implementation ul {
  margin: 0;
  padding-left: 1.2rem;
}

.aqs-implementation li {
  font-size: 0.85rem;
  line-height: 1.5;
  color: #4a5568;
  margin-bottom: 0.5rem;
}

.key-characteristics {
  margin-top: 1rem;
}

.key-characteristics h6 {
  color: #2d3748;
  margin: 0 0 0.75rem 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.characteristic-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: #fef5e7;
  border-radius: 6px;
  border-left: 3px solid #f6ad55;
}

.characteristic-icon {
  font-size: 1.2rem;
  flex-shrink: 0;
}

/* CountDownLatch类比样式 */
.countdown-analogy {
  background: #fff5f5;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border-left: 4px solid #e53e3e;
}

.analogy-scenario {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.analogy-step {
  background: white;
  border-radius: 6px;
  padding: 1rem;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.analogy-step .step-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  display: block;
}

.analogy-step h6 {
  color: #2d3748;
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.analogy-step p {
  color: #4a5568;
  margin: 0;
  font-size: 0.85rem;
  line-height: 1.4;
}

/* CountDownLatch对比样式 */
.countdown-comparison {
  background: #edf2f7;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border-left: 4px solid #a0aec0;
}

.comparison-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.comparison-item {
  background: white;
  border-radius: 6px;
  padding: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.comparison-item h6 {
  color: #2d3748;
  margin: 0 0 0.75rem 0;
  font-size: 0.9rem;
  font-weight: 600;
  text-align: center;
}

.comparison-details {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.diff-point {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 0.5rem;
  background: #f7fafc;
  border-radius: 4px;
}

.diff-label {
  font-weight: 600;
  color: #2d3748;
  font-size: 0.8rem;
}

.diff-point span:last-child {
  font-size: 0.85rem;
  color: #4a5568;
  line-height: 1.4;
}

/* 实现步骤样式 */
.implementation-steps {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin: 1rem 0;
}

.step-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: #f7fafc;
  border-radius: 6px;
}

.step-num {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #4299e1;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: bold;
  flex-shrink: 0;
}

.step-item span:last-child {
  font-size: 0.85rem;
  color: #2d3748;
  line-height: 1.4;
}

/* ConcurrentHashMap样式 */
.concept-definition {
  background: #f0fff4;
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 1rem;
  border-left: 3px solid #48bb78;
}

.design-principle {
  background: #e6fffa;
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 1rem;
  border-left: 3px solid #38b2ac;
}

.timeline-item.java7 {
  border-left: 3px solid #f6ad55;
}

.timeline-item.java8 {
  border-left: 3px solid #4299e1;
}

.timeline-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.version-badge {
  background: #667eea;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.7rem;
  font-weight: 600;
}

.timeline-item.java7 .version-badge {
  background: #f6ad55;
}

.timeline-item.java8 .version-badge {
  background: #4299e1;
}

.timeline-header h7 {
  color: #2d3748;
  font-size: 0.9rem;
  font-weight: 600;
  margin: 0;
}

.timeline-content p {
  margin: 0 0 0.5rem 0;
  font-size: 0.85rem;
  line-height: 1.4;
  color: #4a5568;
}

.bank-analogy {
  background: #fef5e7;
  border-radius: 6px;
  padding: 1rem;
  margin-top: 1rem;
  border-left: 3px solid #f6ad55;
}

.bank-analogy h6 {
  color: #2d3748;
  margin: 0 0 0.75rem 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.bank-analogy .analogy-comparison {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.bank-analogy .analogy-item {
  padding: 0.75rem;
  background: white;
  border-radius: 4px;
  font-size: 0.85rem;
  line-height: 1.4;
  color: #4a5568;
}

/* 危险场景样式 */
.danger-illustration {
  background: #fed7d7;
  border-radius: 6px;
  padding: 1rem;
  margin: 1rem 0;
  border-left: 3px solid #e53e3e;
}

.danger-illustration h7 {
  color: #c53030;
  margin: 0 0 0.75rem 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.scenario-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 0.75rem;
}

.scenario-step {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem;
  background: white;
  border-radius: 4px;
  font-size: 0.8rem;
  color: #2d3748;
}

.scenario-step .step-icon {
  font-size: 1.2rem;
  flex-shrink: 0;
}

.industry-choice {
  background: #e6fffa;
  border-radius: 6px;
  padding: 1rem;
  margin-top: 1rem;
  border-left: 3px solid #38b2ac;
}

.industry-choice h8 {
  color: #2d3748;
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.industry-choice p {
  margin: 0;
  font-size: 0.85rem;
  line-height: 1.5;
  color: #4a5568;
}

/* 响应式设计增强 */
@media (max-width: 768px) {
  .analogy-scenario {
    grid-template-columns: 1fr;
  }

  .comparison-grid {
    grid-template-columns: 1fr;
  }

  .scenario-steps {
    grid-template-columns: 1fr;
  }

  .step-item {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }

  .step-num {
    margin-bottom: 0.5rem;
  }
}

/* CopyOnWriteArrayList深度样式 */
.cow-definition {
  background: #f0fff4;
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 1rem;
  border-left: 3px solid #48bb78;
}

.cow-principle {
  background: #e6fffa;
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 1rem;
  border-left: 3px solid #38b2ac;
}

.principle-details {
  margin-top: 1rem;
}

.principle-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 0.75rem;
  background: white;
  border-radius: 6px;
  margin-bottom: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.principle-num {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #38b2ac;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.8rem;
  font-weight: bold;
  flex-shrink: 0;
}

.principle-item span:last-child {
  font-size: 0.85rem;
  color: #2d3748;
  line-height: 1.4;
}

.cow-analogy {
  background: #fef5e7;
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 1rem;
  border-left: 3px solid #f6ad55;
}

.cow-analogy .analogy-scenario {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.cow-analogy .analogy-step {
  background: white;
  border-radius: 6px;
  padding: 1rem;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.cow-analogy .step-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  display: block;
}

.cow-analogy .step-content h7 {
  color: #2d3748;
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.cow-analogy .step-content p {
  color: #4a5568;
  margin: 0;
  font-size: 0.85rem;
  line-height: 1.4;
}

.cow-characteristics {
  margin-top: 1rem;
}

.characteristics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.characteristic {
  background: white;
  border-radius: 6px;
  padding: 1rem;
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.char-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.char-content h7 {
  color: #2d3748;
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.char-content p {
  color: #4a5568;
  margin: 0;
  font-size: 0.85rem;
  line-height: 1.4;
}

/* 响应式设计增强 */
@media (max-width: 768px) {
  .cow-analogy .analogy-scenario {
    grid-template-columns: 1fr;
  }

  .characteristics-grid {
    grid-template-columns: 1fr;
  }

  .principle-item {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }

  .principle-num {
    margin-bottom: 0.5rem;
  }
}

/* Future与异步编程样式 */
.future-evolution {
  margin: 2rem 0;
}

.future-evolution .evolution-comparison {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 2rem;
  align-items: center;
  margin: 2rem 0;
}

.concept-explanation {
  background: #f7fafc;
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 1rem;
  border-left: 3px solid #4299e1;
}

.analogy-simple {
  background: #e6fffa;
  border-radius: 4px;
  padding: 0.75rem;
  margin-top: 0.75rem;
  font-size: 0.85rem;
  line-height: 1.4;
  color: #2d3748;
}

.limitations {
  background: #fef5e7;
  border-radius: 6px;
  padding: 1rem;
  border-left: 3px solid #f6ad55;
}

.advantages {
  background: #f0fff4;
  border-radius: 6px;
  padding: 1rem;
  border-left: 3px solid #48bb78;
}

.limitations h6,
.advantages h6 {
  color: #2d3748;
  margin: 0 0 0.75rem 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.limitations ul,
.advantages ul {
  margin: 0;
  padding-left: 1.2rem;
}

.limitations li,
.advantages li {
  font-size: 0.85rem;
  line-height: 1.4;
  color: #4a5568;
  margin-bottom: 0.5rem;
}

.completable-future-deep-dive {
  margin: 2rem 0;
}

.core-principle {
  background: #edf2f7;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  border-left: 4px solid #a0aec0;
}

.core-principle h5 {
  color: #2d3748;
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
}

.core-principle p {
  color: #4a5568;
  margin: 0 0 0.75rem 0;
  font-size: 0.9rem;
  line-height: 1.5;
}

.method-comparison {
  margin: 2rem 0;
}

.methods-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
}

.method-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-top: 4px solid #667eea;
}

.method-card.sync {
  border-top-color: #4299e1;
}

.method-card.async {
  border-top-color: #48bb78;
}

.method-card.combine {
  border-top-color: #f6ad55;
}

.method-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 2px solid #f7fafc;
}

.method-icon {
  font-size: 1.5rem;
}

.method-header h6 {
  color: #2d3748;
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
}

.method-content {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.method-content .method-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: #f7fafc;
  border-radius: 6px;
}

.method-content .method-item code {
  background: #2d3748;
  color: #e2e8f0;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.8rem;
  font-weight: 600;
  min-width: 140px;
  text-align: center;
}

.method-content .method-item span {
  font-size: 0.85rem;
  color: #4a5568;
  line-height: 1.4;
}

/* 响应式设计增强 */
@media (max-width: 768px) {
  .future-evolution .evolution-comparison {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .evolution-arrow {
    transform: rotate(90deg);
    text-align: center;
  }

  .methods-grid {
    grid-template-columns: 1fr;
  }

  .method-content .method-item {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }

  .method-content .method-item code {
    min-width: auto;
  }
}

/* BlockingQueue 样式 */
.blocking-queues-showcase .concept-definition,
.blocking-queues-showcase .deep-analysis,
.blocking-queues-showcase .examples-analogies {
  margin: 2rem 0;
}

.definition-content,
.analysis-content,
.examples-content {
  background: #f7fafc;
  border-radius: 8px;
  padding: 1.5rem;
  margin-top: 1rem;
}

.human-explanation {
  background: #e6fffa;
  border-radius: 6px;
  padding: 1rem;
  margin-top: 1rem;
  border-left: 3px solid #38b2ac;
}

.human-explanation h5 {
  color: #2d3748;
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.value-points {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1rem;
}

.value-point {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
  background: white;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.point-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.value-point div {
  flex: 1;
}

.value-point strong {
  color: #2d3748;
  font-weight: 600;
}

.concrete-case,
.vivid-analogy {
  background: #fef5e7;
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 1rem;
  border-left: 3px solid #f6ad55;
}

.analogy-points {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 1rem;
}

.analogy-point {
  background: white;
  border-radius: 4px;
  padding: 0.75rem;
  font-size: 0.85rem;
  line-height: 1.4;
  color: #2d3748;
}

.analogy-point code {
  background: #2d3748;
  color: #e2e8f0;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-size: 0.8rem;
}

.comparison-intro {
  background: #edf2f7;
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 1rem;
}

.detailed-comparison {
  margin-top: 1rem;
}

.comparison-detail {
  background: #f0fff4;
  border-radius: 6px;
  padding: 1rem;
  border-left: 3px solid #48bb78;
}

.detail-item {
  margin-bottom: 0.75rem;
  font-size: 0.85rem;
  line-height: 1.4;
  color: #2d3748;
}

.detail-item:last-child {
  margin-bottom: 0;
}

/* Future 项目实践踩坑样式 */
.future-pitfalls {
  margin: 2rem 0;
}

.future-pitfalls .pitfall-item {
  background: #fef5e7;
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 1rem;
  border-left: 3px solid #f6ad55;
}

.future-pitfalls .pitfall-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.future-pitfalls .pitfall-icon {
  font-size: 1.2rem;
}

.future-pitfalls h6 {
  color: #2d3748;
  margin: 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.practical-example {
  background: #f0fff4;
  border-radius: 8px;
  padding: 1.5rem;
  margin: 2rem 0;
  border-left: 4px solid #48bb78;
}

.practical-example h5 {
  color: #2d3748;
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
}

.practical-example .code-example {
  background: #2d3748;
  border-radius: 6px;
  padding: 1rem;
  margin-top: 1rem;
  overflow-x: auto;
}

.practical-example .code-example pre {
  margin: 0;
  color: #e2e8f0;
  font-size: 0.8rem;
  line-height: 1.4;
}

/* Executor 框架样式 */
.executor-showcase .concept-definition,
.executor-showcase .deep-analysis,
.executor-showcase .examples-analogies {
  margin: 2rem 0;
}

.concrete-cases {
  background: #f0fff4;
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 1rem;
  border-left: 3px solid #48bb78;
}

.case-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.case-item {
  background: white;
  border-radius: 6px;
  padding: 1rem;
  font-size: 0.85rem;
  line-height: 1.4;
  color: #2d3748;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.case-item strong {
  color: #2d3748;
  font-weight: 600;
}

.horizontal-comparison {
  margin: 2rem 0;
}

.cause-points {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-top: 1rem;
}

.cause-point {
  background: white;
  border-radius: 4px;
  padding: 0.75rem;
  font-size: 0.85rem;
  line-height: 1.4;
  color: #2d3748;
  border-left: 3px solid #f6ad55;
}

.solution-benefits {
  margin-top: 1rem;
}

.solution-benefits h6 {
  color: #2d3748;
  margin: 0 0 0.75rem 0;
  font-size: 0.9rem;
  font-weight: 600;
}

.benefit-item {
  background: #f0fff4;
  border-radius: 4px;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  font-size: 0.85rem;
  line-height: 1.4;
  color: #2d3748;
  border-left: 3px solid #48bb78;
}

.benefit-item strong {
  color: #2d3748;
  font-weight: 600;
}

.solution-tradeoff {
  background: #edf2f7;
  border-radius: 4px;
  padding: 0.75rem;
  margin-top: 1rem;
  font-size: 0.85rem;
  line-height: 1.4;
  color: #2d3748;
  border-left: 3px solid #a0aec0;
}

.pitfall-reasons {
  margin-top: 0.75rem;
}

.pitfall-reasons h6 {
  color: #2d3748;
  margin: 0 0 0.5rem 0;
  font-size: 0.85rem;
  font-weight: 600;
}

.pitfall-reasons ul {
  margin: 0;
  padding-left: 1.2rem;
}

.pitfall-reasons li {
  font-size: 0.8rem;
  line-height: 1.4;
  color: #4a5568;
  margin-bottom: 0.5rem;
}

/* 响应式设计增强 */
@media (max-width: 768px) {
  .case-grid {
    grid-template-columns: 1fr;
  }

  .value-points {
    gap: 0.75rem;
  }

  .value-point {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }

  .point-icon {
    margin-bottom: 0.5rem;
  }
}
</style>
